import { getFirebaseClients } from "@/app/Firebase/init";
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  setDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp,
  type DocumentData,
  type DocumentReference,
  type CollectionReference,
} from "firebase/firestore";

export function getCollectionRef<T = DocumentData>(path: string): CollectionReference<T> {
  const { db } = getFirebaseClients();
  return collection(db, path) as CollectionReference<T>;
}

export function getDocRef<T = DocumentData>(path: string): DocumentReference<T> {
  const { db } = getFirebaseClients();
  return doc(db, path) as DocumentReference<T>;
}

export async function readDoc<T = DocumentData>(path: string): Promise<T | null> {
  const snap = await getDoc(getDocRef<T>(path));
  return snap.exists() ? (snap.data() as T) : null;
}

export async function readCollection<T = DocumentData>(path: string): Promise<T[]> {
  const q = query(getCollectionRef<T>(path));
  const snap = await getDocs(q);
  return snap.docs.map((d) => ({ id: d.id, ...(d.data() as T) }) as T);
}

export async function createDoc<T extends object>(path: string, data: T): Promise<void> {
  await setDoc(getDocRef(path), { ...data, createdAt: serverTimestamp(), updatedAt: serverTimestamp() });
}

export async function addDocToCollection<T extends object>(collectionPath: string, data: T): Promise<DocumentReference> {
  const colRef = getCollectionRef(collectionPath);
  return await addDoc(colRef, { ...data, createdAt: serverTimestamp(), updatedAt: serverTimestamp() });
}

export async function upsertDoc<T extends object>(path: string, data: Partial<T>): Promise<void> {
  await setDoc(getDocRef(path), { ...data, updatedAt: serverTimestamp() }, { merge: true });
}

export async function updateDocFields<T extends object>(path: string, data: Partial<T>): Promise<void> {
  await updateDoc(getDocRef(path), { ...data, updatedAt: serverTimestamp() });
}

export async function deleteDocByPath(path: string): Promise<void> {
  await deleteDoc(getDocRef(path));
}


