import { createDoc, readCollection, updateDocFields, deleteDocByPath, readDoc, upsertDoc } from "./firestoreService";
import { type Timestamp } from "firebase/firestore";

export type LearningToolType = 
  | "flashcards"
  | "trueOrFalse"
  | "twoFactsOneLie"
  | "knowledgeGraphs"
  | "practicalScenarioGuidance"
  | "matchConcepts"
  | "conceptsGuidedLearning";

export type PlanStatus = "draft" | "active" | "completed" | "paused";
export type PlanDifficulty = "Beginner" | "Intermediate" | "Advanced";
export type PlanIntensity = "simple" | "general" | "detailed";

export interface LearningTool {
  id: string;
  name: string;
  type: LearningToolType;
  description: string;
  order: number;
  estimatedTime: string;
  content: any; // Flexible content structure for all tool types
  completed?: boolean;
  completedAt?: Date | Timestamp;
  progress?: number; // 0-100
}

export interface LearningPlan {
  id: string;
  title: string;
  description: string;
  topic: string;
  intensity: PlanIntensity;
  difficulty: PlanDifficulty;
  estimatedDuration: string;
  status: PlanStatus;
  progressPercentage: number;
  
  // Content
  tools: LearningTool[];
  learningObjectives: string[];
  prerequisites?: string[];
  focusAreas?: string[];
  
  // Metadata
  userId: string;
  certificateId: string;
  certificateContext?: {
    id: string;
    name: string;
    provider: string;
    description: string;
  };
  
  // Timestamps
  createdAt?: Date | Timestamp;
  updatedAt?: Date | Timestamp;
  startedAt?: Date | Timestamp;
  completedAt?: Date | Timestamp;
}

export interface CreatePlanInput {
  topic: string;
  intensity: PlanIntensity;
  focusAreas?: string[];
  certificateId: string;
  certificateContext?: {
    id: string;
    name: string;
    provider: string;
    description: string;
  };
}

/**
 * Creates a new learning plan for a user
 */
export async function createLearningPlan(
  userId: string,
  input: CreatePlanInput
): Promise<string> {
  const planId = `plan_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  const path = `users/${userId}/learningPlans/${planId}`;

  const plan: Omit<LearningPlan, 'id' | 'createdAt' | 'updatedAt'> = {
    title: `Learning Plan: ${input.topic}`,
    description: `AI-generated learning plan for ${input.topic}`,
    topic: input.topic,
    intensity: input.intensity,
    difficulty: "Beginner", // Will be updated by AI
    estimatedDuration: "TBD", // Will be updated by AI
    status: "draft",
    progressPercentage: 0,
    tools: [],
    learningObjectives: [],
    prerequisites: input.focusAreas,
    focusAreas: input.focusAreas,
    userId,
    certificateId: input.certificateId,
    certificateContext: input.certificateContext,
  };

  await createDoc(path, { id: planId, ...plan });
  return planId;
}

/**
 * Updates a learning plan with AI-generated content
 */
export async function updateLearningPlanWithAIContent(
  userId: string,
  planId: string,
  aiContent: {
    title: string;
    description: string;
    estimatedDuration: string;
    difficulty: PlanDifficulty;
    tools: LearningTool[];
    learningObjectives: string[];
    prerequisites?: string[];
  }
): Promise<void> {
  const path = `users/${userId}/learningPlans/${planId}`;
  await updateDocFields<LearningPlan>(path, {
    ...aiContent,
    status: "active" as PlanStatus,
  });
}

/**
 * Gets all learning plans for a user
 */
export async function getUserLearningPlans(userId: string): Promise<LearningPlan[]> {
  const path = `users/${userId}/learningPlans`;
  const plans = await readCollection<LearningPlan>(path);
  return plans.sort((a, b) => {
    const aTime = a.createdAt instanceof Date ? a.createdAt.getTime() : a.createdAt?.toDate().getTime() || 0;
    const bTime = b.createdAt instanceof Date ? b.createdAt.getTime() : b.createdAt?.toDate().getTime() || 0;
    return bTime - aTime; // Most recent first
  });
}

/**
 * Gets learning plans for a specific certificate
 */
export async function getPlansByCertificate(userId: string, certificateId: string): Promise<LearningPlan[]> {
  const allPlans = await getUserLearningPlans(userId);
  return allPlans.filter(plan => plan.certificateId === certificateId);
}

/**
 * Gets a specific learning plan
 */
export async function getLearningPlan(userId: string, planId: string): Promise<LearningPlan | null> {
  const path = `users/${userId}/learningPlans/${planId}`;
  return await readDoc<LearningPlan>(path);
}

/**
 * Updates plan progress
 */
export async function updatePlanProgress(
  userId: string,
  planId: string,
  toolId: string,
  progress: number,
  completed: boolean = false
): Promise<void> {
  const plan = await getLearningPlan(userId, planId);
  if (!plan) return;

  // Update tool progress
  const updatedTools = plan.tools.map(tool => {
    if (tool.id === toolId) {
      return {
        ...tool,
        progress,
        completed,
        completedAt: completed ? new Date() : tool.completedAt
      };
    }
    return tool;
  });

  // Calculate overall progress
  const totalProgress = updatedTools.reduce((sum, tool) => sum + (tool.progress || 0), 0);
  const progressPercentage = Math.round(totalProgress / updatedTools.length);

  // Check if plan is completed
  const allCompleted = updatedTools.every(tool => tool.completed);
  const status: PlanStatus = allCompleted ? "completed" : "active";

  const path = `users/${userId}/learningPlans/${planId}`;
  await updateDocFields<LearningPlan>(path, {
    tools: updatedTools,
    progressPercentage,
    status,
    completedAt: allCompleted ? new Date() : undefined,
  });
}

/**
 * Deletes a learning plan
 */
export async function deleteLearningPlan(userId: string, planId: string): Promise<void> {
  const path = `users/${userId}/learningPlans/${planId}`;
  await deleteDocByPath(path);
}

/**
 * Updates plan status
 */
export async function updatePlanStatus(
  userId: string,
  planId: string,
  status: PlanStatus
): Promise<void> {
  const path = `users/${userId}/learningPlans/${planId}`;
  const updates: Partial<LearningPlan> = { status };
  
  if (status === "active" && !await getLearningPlan(userId, planId)?.then(p => p?.startedAt)) {
    updates.startedAt = new Date();
  }
  
  if (status === "completed") {
    updates.completedAt = new Date();
    updates.progressPercentage = 100;
  }

  await updateDocFields<LearningPlan>(path, updates);
}
