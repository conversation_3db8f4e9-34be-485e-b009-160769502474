import { createDoc, addDocToCollection, readDoc, updateDocFields, deleteDocByPath, readCollection } from "./firestoreService";
import { type Timestamp } from "firebase/firestore";

export type LearningToolType = 
  | "flashcards"
  | "trueOrFalse" 
  | "twoFactsOneLie"
  | "knowledgeGraphs"
  | "practicalScenarioGuidance"
  | "matchConcepts"
  | "conceptsGuidedLearning";

export type PlanStatus = "draft" | "active" | "completed" | "paused";
export type Difficulty = "Beginner" | "Intermediate" | "Advanced";

export interface LearningTool {
  id: string;
  name: string;
  type: LearningToolType;
  description: string;
  order: number;
  estimatedTime: string;
  content: any; // Tool-specific content structure
  completed?: boolean;
  completedAt?: Timestamp | Date;
  score?: number;
  attempts?: number;
}

export interface LearningPlan {
  id: string;
  title: string;
  description: string;
  topic: string;
  intensity: "detailed" | "general" | "simple";
  focusAreas?: string[];
  estimatedDuration: string;
  difficulty: Difficulty;
  status: PlanStatus;
  
  // Tools and content
  tools: LearningTool[];
  learningObjectives: string[];
  prerequisites?: string[];
  
  // Progress tracking
  currentToolIndex: number;
  completedTools: number;
  totalTimeSpent?: number; // in minutes
  progressPercentage: number;
  
  // Certificate context
  certificateId?: string;
  certificateName?: string;
  certificateProvider?: string;
  
  // Metadata
  createdAt?: Timestamp | Date;
  updatedAt?: Timestamp | Date;
  createdBy: string; // user ID
  
  // AI generation metadata
  aiModel?: string;
  generationPrompt?: string;
}

export interface PlanProgress {
  planId: string;
  toolId: string;
  userId: string;
  completed: boolean;
  score?: number;
  timeSpent: number; // in minutes
  attempts: number;
  lastAttemptAt: Timestamp | Date;
  responses?: any; // Tool-specific response data
}

// Create a new learning plan
export async function createLearningPlan(
  userId: string,
  planData: Omit<LearningPlan, 'id' | 'createdAt' | 'updatedAt' | 'createdBy' | 'currentToolIndex' | 'completedTools' | 'progressPercentage'>
): Promise<LearningPlan> {
  const newPlan: Omit<LearningPlan, 'id'> = {
    ...planData,
    createdBy: userId,
    currentToolIndex: 0,
    completedTools: 0,
    progressPercentage: 0,
    status: 'active',
  };

  // Create the document and get the auto-generated ID
  const docRef = await addDocToCollection(`users/${userId}/learningPlans`, newPlan);

  // Read back the created document with the ID
  const createdPlan = await readDoc<LearningPlan>(`users/${userId}/learningPlans/${docRef.id}`);
  if (!createdPlan) {
    throw new Error('Failed to create learning plan');
  }

  return { ...createdPlan, id: docRef.id };
}

// Get a specific learning plan
export async function getLearningPlan(userId: string, planId: string): Promise<LearningPlan | null> {
  const planPath = `users/${userId}/learningPlans/${planId}`;
  return await readDoc<LearningPlan>(planPath);
}

// Get all learning plans for a user
export async function getUserLearningPlans(userId: string): Promise<LearningPlan[]> {
  const plansPath = `users/${userId}/learningPlans`;
  return await readCollection<LearningPlan>(plansPath);
}

// Update plan progress
export async function updatePlanProgress(
  userId: string, 
  planId: string, 
  updates: Partial<Pick<LearningPlan, 'currentToolIndex' | 'completedTools' | 'progressPercentage' | 'status' | 'totalTimeSpent'>>
): Promise<void> {
  const planPath = `users/${userId}/learningPlans/${planId}`;
  await updateDocFields(planPath, updates);
}

// Mark a tool as completed
export async function completeToolInPlan(
  userId: string, 
  planId: string, 
  toolId: string, 
  score?: number,
  timeSpent?: number
): Promise<void> {
  const plan = await getLearningPlan(userId, planId);
  if (!plan) throw new Error('Plan not found');

  const toolIndex = plan.tools.findIndex(tool => tool.id === toolId);
  if (toolIndex === -1) throw new Error('Tool not found in plan');

  // Update the specific tool
  const updatedTools = [...plan.tools];
  updatedTools[toolIndex] = {
    ...updatedTools[toolIndex],
    completed: true,
    completedAt: new Date(),
    score,
    attempts: (updatedTools[toolIndex].attempts || 0) + 1,
  };

  // Calculate new progress
  const completedCount = updatedTools.filter(tool => tool.completed).length;
  const progressPercentage = Math.round((completedCount / updatedTools.length) * 100);
  const newCurrentIndex = Math.min(toolIndex + 1, updatedTools.length - 1);

  // Determine new status
  let newStatus: PlanStatus = plan.status;
  if (completedCount === updatedTools.length) {
    newStatus = 'completed';
  }

  const updates = {
    tools: updatedTools,
    completedTools: completedCount,
    progressPercentage,
    currentToolIndex: newCurrentIndex,
    status: newStatus,
    totalTimeSpent: (plan.totalTimeSpent || 0) + (timeSpent || 0),
  };

  await updateDocFields(`users/${userId}/learningPlans/${planId}`, updates);
}

// Record tool progress (for partial completion tracking)
export async function recordToolProgress(progressData: Omit<PlanProgress, 'lastAttemptAt'>): Promise<void> {
  const progressDoc = {
    ...progressData,
    lastAttemptAt: new Date(),
  };

  // Use planId_toolId as document ID for easy retrieval
  const docId = `${progressData.planId}_${progressData.toolId}`;
  const progressPath = `users/${progressData.userId}/learningProgress/${docId}`;
  await createDoc(progressPath, progressDoc);
}

// Get tool progress
export async function getToolProgress(userId: string, planId: string, toolId: string): Promise<PlanProgress | null> {
  const docId = `${planId}_${toolId}`;
  const progressPath = `users/${userId}/learningProgress/${docId}`;
  return await readDoc<PlanProgress>(progressPath);
}

// Delete a learning plan
export async function deleteLearningPlan(userId: string, planId: string): Promise<void> {
  const planPath = `users/${userId}/learningPlans/${planId}`;
  await deleteDocByPath(planPath);
}

// Get plans by certificate
export async function getPlansByCertificate(userId: string, certificateId: string): Promise<LearningPlan[]> {
  const plans = await getUserLearningPlans(userId);
  return plans.filter(plan => plan.certificateId === certificateId);
}

// Update plan status
export async function updatePlanStatus(userId: string, planId: string, status: PlanStatus): Promise<void> {
  const planPath = `users/${userId}/learningPlans/${planId}`;
  await updateDocFields(planPath, { status });
}
