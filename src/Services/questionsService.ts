import { createDoc, readCollection, updateDocFields, deleteDocByPath, readDoc, upsertDoc } from "./firestoreService";

export interface QuestionInput {
  question: string;
  correct: "A" | "B" | "C" | "D";
  choices: [string, string, string, string];
  rowNumber?: number;
  // Optional AI-generated categorization fields
  topic?: string;
  difficulty?: "Easy" | "Medium" | "Hard";
  tags?: string[];
}

export interface QuestionRecord extends QuestionInput {
  id: string;
  createdAt?: any;
  updatedAt?: any;
  // Optional AI explanation cached on the question document to avoid repeated calls
  aiExplanation?: QuestionExplanation;
}

// Certificate-level taxonomy configuration (persisted for consistent categorization)
export interface CertificateTaxonomy {
  groups: string[];
  version?: number;
  strict?: boolean; // if true, UI/AI should clamp to these groups
  maxTagsPerQuestion?: number; // 1 or 2 typically
}

// Compact AI explanation structures for caching
export interface AIAnswerAnalysis {
  key: "A" | "B" | "C" | "D";
  verdict: "correct" | "plausible" | "trap" | "incorrect";
  summary: string; // single short line
  bullets: string[]; // 1-3 concise bullets
}

export interface QuestionExplanation {
  answers: AIAnswerAnalysis[];
  knowledgeAreas: string[]; // 1-5 chips
  similarQuestionThemes: string[]; // 0-3 short items
  model?: string; // e.g., gemini-2.5-pro
  updatedAt?: any;
}

// Shared cache document for AI analysis across certificates/users
export interface SharedQuestionAnalysisDoc {
  explanation: QuestionExplanation;
  question: string;
  choices: [string, string, string, string];
  correct: "A" | "B" | "C" | "D";
  // Optional metadata to help with debugging/traceability
  lastComputedAt?: any;
  model?: string;
}

/**
 * Creates a new question document under certificates/{certificateId}/questions/{questionId}
 */
export async function createQuestion(
  certificateId: string,
  input: QuestionInput
): Promise<string> {
  // Use the rowNumber to generate a meaningful ID, or get the next available number
  let questionNumber = input.rowNumber;

  if (!questionNumber) {
    // If no rowNumber provided, get the next available number
    const existingQuestions = await listQuestions(certificateId);
    const maxNumber = existingQuestions.reduce((max, q) => {
      if (q.rowNumber && q.rowNumber > max) return q.rowNumber;
      return max;
    }, 0);
    questionNumber = maxNumber + 1;
  }

  let questionId = `Q${questionNumber}`;
  let path = `certificates/${certificateId}/questions/${questionId}`;

  // Check if a question with this ID already exists
  const existingQuestion = await readDoc(path);
  if (existingQuestion) {
    // If it exists, find the next available number
    const existingQuestions = await listQuestions(certificateId);
    const usedNumbers = new Set(existingQuestions.map(q => q.rowNumber).filter(Boolean));

    let newNumber = questionNumber;
    while (usedNumbers.has(newNumber)) {
      newNumber++;
    }

    questionNumber = newNumber;
    questionId = `Q${questionNumber}`;
    path = `certificates/${certificateId}/questions/${questionId}`;
  }

  // Ensure rowNumber is set in the data
  const questionData = {
    id: questionId,
    ...input,
    rowNumber: questionNumber
  };

  await createDoc(path, questionData);
  return questionId;
}

/**
 * Gets a specific question by ID
 */
export async function getQuestion(certificateId: string, questionId: string): Promise<QuestionRecord | null> {
  const path = `certificates/${certificateId}/questions/${questionId}`;
  return await readDoc<QuestionRecord>(path);
}

/**
 * Lists all questions for a specific certificate
 */
export async function listQuestions(certificateId: string): Promise<QuestionRecord[]> {
  const path = `certificates/${certificateId}/questions`;
  const docs = await readCollection<QuestionRecord>(path);

  // Sort by rowNumber if available, otherwise by creation date
  return docs.sort((a, b) => {
    if (a.rowNumber !== undefined && b.rowNumber !== undefined) {
      return a.rowNumber - b.rowNumber;
    }
    return Number(new Date(b.createdAt || 0)) - Number(new Date(a.createdAt || 0));
  });
}

/**
 * Updates a question
 */
export async function updateQuestion(
  certificateId: string,
  questionId: string,
  data: Partial<QuestionInput>
): Promise<void> {
  const path = `certificates/${certificateId}/questions/${questionId}`;
  await updateDocFields<QuestionRecord>(path, data);
}

/**
 * Reads cached AI explanation for a question, if available.
 */
export async function getQuestionExplanation(
  certificateId: string,
  questionId: string
): Promise<QuestionExplanation | null> {
  const path = `certificates/${certificateId}/questions/${questionId}`;
  const doc = await readDoc<QuestionRecord>(path);
  return (doc && (doc as any).aiExplanation) ? ((doc as any).aiExplanation as QuestionExplanation) : null;
}

/**
 * Updates the question with an AI explanation payload to enable caching.
 */
export async function updateQuestionExplanation(
  certificateId: string,
  questionId: string,
  explanation: QuestionExplanation
): Promise<void> {
  const path = `certificates/${certificateId}/questions/${questionId}`;
  await updateDocFields<QuestionRecord>(path, { aiExplanation: explanation } as Partial<QuestionRecord>);
}

/**
 * Updates only categorization fields for a question.
 * If merge is true, merges incoming tags with existing tags to preserve history.
 */
export async function updateQuestionCategories(
  certificateId: string,
  questionId: string,
  categories: { tags?: string[]; topic?: string; difficulty?: "Easy" | "Medium" | "Hard" },
  merge: boolean = true
): Promise<void> {
  const path = `certificates/${certificateId}/questions/${questionId}`;
  let nextTags = categories.tags;
  if (merge && categories.tags) {
    const existing = await readDoc<QuestionRecord>(path);
    const prev = Array.isArray(existing?.tags) ? existing!.tags : [];
    const set = new Set<string>([...prev, ...categories.tags]);
    nextTags = Array.from(set);
  }

  await updateDocFields<QuestionRecord>(path, {
    // Only include defined fields to avoid Firestore undefined errors
    ...(typeof nextTags !== "undefined" ? { tags: nextTags } : {}),
    ...(typeof categories.topic !== "undefined" ? { topic: categories.topic } : {}),
    ...(typeof categories.difficulty !== "undefined" ? { difficulty: categories.difficulty } : {}),
  });
}

/**
 * Reads the saved taxonomy configuration for a certificate.
 */
export async function getCertificateTaxonomy(certificateId: string): Promise<CertificateTaxonomy | null> {
  // Store taxonomy as a document under a config subcollection to keep path segments even
  const doc = await readDoc<CertificateTaxonomy>(`certificates/${certificateId}/config/taxonomy`);
  return doc;
}

/**
 * Saves or updates the taxonomy configuration for a certificate.
 */
export async function setCertificateTaxonomy(
  certificateId: string,
  taxonomy: CertificateTaxonomy
): Promise<void> {
  await upsertDoc<CertificateTaxonomy>(`certificates/${certificateId}/config/taxonomy`, taxonomy);
}

/**
 * Deletes a question
 */
export async function deleteQuestion(certificateId: string, questionId: string): Promise<void> {
  const path = `certificates/${certificateId}/questions/${questionId}`;
  await deleteDocByPath(path);
}

/**
 * Bulk creates multiple questions for a certificate
 */
export async function bulkCreateQuestions(
  certificateId: string,
  questions: QuestionInput[]
): Promise<string[]> {
  const questionIds: string[] = [];

  // Use the individual createQuestion function to handle conflicts properly
  for (const question of questions) {
    const questionId = await createQuestion(certificateId, question);
    questionIds.push(questionId);
  }

  return questionIds;
}

/**
 * Gets the total count of questions for a certificate
 */
export async function getQuestionsCount(certificateId: string): Promise<number> {
  const questions = await listQuestions(certificateId);
  return questions.length;
}

/**
 * Shared AI analysis cache: retrieves the explanation by a stable hash key.
 * Top-level collection to enable reuse across certificates and users.
 */
export async function getSharedQuestionExplanation(hash: string): Promise<QuestionExplanation | null> {
  const doc = await readDoc<SharedQuestionAnalysisDoc>(`questions/${hash}`);
  return doc?.explanation ?? null;
}

/**
 * Writes/updates the shared AI analysis explanation under the top-level questions collection.
 */
export async function setSharedQuestionExplanation(
  hash: string,
  payload: Omit<SharedQuestionAnalysisDoc, "lastComputedAt"> & { lastComputedAt?: any }
): Promise<void> {
  await upsertDoc<SharedQuestionAnalysisDoc>(`questions/${hash}`, {
    ...payload,
    lastComputedAt: new Date(),
  });
}
