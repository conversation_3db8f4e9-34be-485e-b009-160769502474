"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useUser } from "@/hooks/useUser";
import { getUserExamAttempts, type ExamAttempt } from "@/Services/examAttemptsService";
import { getQuestion, type QuestionRecord } from "@/Services/questionsService";
import { AlertTriangle, BookOpen, Target, TrendingDown, ChevronDown, ChevronUp } from "lucide-react";

interface WrongQuestionData {
  questionId: string;
  question: QuestionRecord | null;
  wrongCount: number;
  attempts: string[]; // attempt IDs where this question was wrong
}

export default function RepairCenter() {
  const params = useParams();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [wrongQuestions, setWrongQuestions] = useState<WrongQuestionData[]>([]);
  const [totalAttempts, setTotalAttempts] = useState(0);
  const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());

  const certificateId = Array.isArray(params?.framework)
    ? params?.framework[0]
    : (params?.framework as string);

  useEffect(() => {
    if (user && certificateId) {
      loadWrongQuestions();
    }
  }, [user, certificateId]);

  const toggleCard = (questionId: string) => {
    const newExpanded = new Set(expandedCards);
    if (newExpanded.has(questionId)) {
      newExpanded.delete(questionId);
    } else {
      newExpanded.add(questionId);
    }
    setExpandedCards(newExpanded);
  };

  const loadWrongQuestions = async () => {
    if (!user || !certificateId) return;

    try {
      setLoading(true);
      setError(null);

      // Get all exam attempts for this user and certificate
      const attempts = await getUserExamAttempts(user.uid, certificateId);
      const completedAttempts = attempts.filter(attempt => attempt.status === "completed");

      setTotalAttempts(completedAttempts.length);

      if (completedAttempts.length === 0) {
        setWrongQuestions([]);
        return;
      }

      // Collect all wrong answers across all attempts
      const wrongAnswersMap = new Map<string, { count: number; attempts: string[] }>();

      completedAttempts.forEach(attempt => {
        attempt.answers.forEach(answer => {
          if (!answer.isCorrect) {
            const existing = wrongAnswersMap.get(answer.questionId);
            if (existing) {
              existing.count++;
              if (!existing.attempts.includes(attempt.id)) {
                existing.attempts.push(attempt.id);
              }
            } else {
              wrongAnswersMap.set(answer.questionId, {
                count: 1,
                attempts: [attempt.id]
              });
            }
          }
        });
      });

      // Load question details for each wrong question
      const wrongQuestionPromises = Array.from(wrongAnswersMap.entries()).map(
        async ([questionId, data]) => {
          const question = await getQuestion(certificateId, questionId);
          return {
            questionId,
            question,
            wrongCount: data.count,
            attempts: data.attempts
          };
        }
      );

      const wrongQuestionsData = await Promise.all(wrongQuestionPromises);

      // Sort by wrong count (most wrong first), then by question text
      const sortedWrongQuestions = wrongQuestionsData
        .filter(item => item.question !== null) // Filter out questions that couldn't be loaded
        .sort((a, b) => {
          if (b.wrongCount !== a.wrongCount) {
            return b.wrongCount - a.wrongCount;
          }
          return (a.question?.question || "").localeCompare(b.question?.question || "");
        });

      setWrongQuestions(sortedWrongQuestions);
    } catch (err) {
      console.error("Error loading wrong questions:", err);
      setError("Failed to load repair center data");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="w-full rounded-xl border border-gray-100 bg-white p-8 shadow-sm">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-grey">Loading repair center...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full rounded-xl border border-gray-100 bg-white p-8 shadow-sm">
        <div className="flex items-center justify-center py-12">
          <AlertTriangle className="h-8 w-8 text-red-500 mr-3" />
          <span className="text-red-600">{error}</span>
        </div>
      </div>
    );
  }

  if (totalAttempts === 0) {
    return (
      <div className="w-full rounded-xl border border-gray-100 bg-white p-8 shadow-sm">
        <div className="text-center py-12">
          <BookOpen className="h-16 w-16 text-grey mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-charcoal mb-2">No Exam Attempts Yet</h3>
          <p className="text-grey">Complete some exams first to see questions that need repair.</p>
        </div>
      </div>
    );
  }

  if (wrongQuestions.length === 0) {
    return (
      <div className="w-full rounded-xl border border-gray-100 bg-white p-8 shadow-sm">
        <div className="text-center py-12">
          <Target className="h-16 w-16 text-accent mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-charcoal mb-2">Perfect Performance!</h3>
          <p className="text-grey">You haven't gotten any questions wrong across {totalAttempts} attempt{totalAttempts !== 1 ? 's' : ''}.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="rounded-xl bg-white p-6 shadow-sm">
        <div className="flex items-center gap-3 mb-2">
          <div className="bg-red-50 rounded-xl p-2">
            <TrendingDown className="h-6 w-6 text-red-600" />
          </div>
          <h2 className="text-2xl font-semibold text-charcoal">Repair Center</h2>
        </div>
        <p className="text-grey">
          Questions you got wrong across {totalAttempts} exam attempt{totalAttempts !== 1 ? 's' : ''}.
          Click on question numbers to expand details.
        </p>
        <div className="mt-4 flex items-center gap-4 text-sm">
          <span className="text-charcoal font-medium">{wrongQuestions.length} unique questions need attention</span>
          <span className="text-grey">•</span>
          <span className="text-grey">
            {wrongQuestions.reduce((sum, q) => sum + q.wrongCount, 0)} total wrong answers
          </span>
        </div>
      </div>

      {/* Question Cards Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
        {wrongQuestions.map((item, index) => {
          const isExpanded = expandedCards.has(item.questionId);

          return (
            <div key={item.questionId} className={`transition-all duration-300 ${
              isExpanded ? 'col-span-full' : ''
            }`}>
              {/* Question Number Card */}
              <div
                onClick={() => toggleCard(item.questionId)}
                className={`relative bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer ${
                  isExpanded ? 'ring-2 ring-primary' : 'hover:scale-105'
                }`}
              >
                <div className="p-4 text-center">
                  <div className="w-12 h-12 mx-auto rounded-full bg-red-100 text-red-600 font-bold text-lg flex items-center justify-center mb-2">
                    {index + 1}
                  </div>
                  <div className="text-xs text-red-600 font-medium">
                    Wrong {item.wrongCount}x
                  </div>
                  <div className="text-xs text-grey mt-1">
                    {item.attempts.length} attempt{item.attempts.length !== 1 ? 's' : ''}
                  </div>
                </div>

                {/* Expand/Collapse Icon */}
                <div className="absolute top-2 right-2">
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4 text-grey" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-grey" />
                  )}
                </div>
              </div>

              {/* Expanded Content */}
              {isExpanded && item.question && (
                <div className="mt-4 bg-white rounded-xl shadow-sm p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 rounded-full bg-red-100 text-red-600 font-semibold text-sm flex items-center justify-center">
                      {index + 1}
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Wrong {item.wrongCount} time{item.wrongCount !== 1 ? 's' : ''}
                      </span>
                      <span className="text-xs text-grey">
                        in {item.attempts.length} attempt{item.attempts.length !== 1 ? 's' : ''}
                      </span>
                    </div>
                  </div>

                  <h4 className="text-charcoal font-medium mb-4 leading-relaxed">
                    {item.question.question}
                  </h4>

                  <div className="space-y-3">
                    <p className="text-sm text-grey font-medium">Answer Choices:</p>
                    <div className="grid gap-2">
                      {item.question.choices.map((choice, choiceIndex) => {
                        const choiceLetter = ["A", "B", "C", "D"][choiceIndex];
                        const isCorrect = item.question?.correct === choiceLetter;

                        return (
                          <div
                            key={choiceIndex}
                            className={`p-3 rounded-lg border ${
                              isCorrect
                                ? 'bg-accent/10 border-accent text-charcoal'
                                : 'bg-gray-50 border-gray-200 text-grey'
                            }`}
                          >
                            <div className="flex items-center gap-2">
                              <span className={`font-semibold text-sm ${
                                isCorrect ? 'text-accent' : 'text-grey'
                              }`}>
                                {choiceLetter}:
                              </span>
                              <span className="text-sm">{choice}</span>
                              {isCorrect && (
                                <span className="ml-auto text-xs font-medium text-accent">
                                  Correct Answer
                                </span>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}


