"use client";

import { useEffect, useMemo, useState } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useParams } from "next/navigation";
import { useRouter } from "@/i18n/navigation";
import { getCertificateTaxonomy, type CertificateTaxonomy } from "@/Services/questionsService";
import { createLearningPlan } from "@/Services/planService";
import { useUser } from "@/hooks/useUser";
import TextGenerateEffect from "@/components/ui/effects/TextGenerateEffect";
import { toast } from "sonner";

// Using the shared TextGenerateEffect component

export default function LearningHub() {
  const params = useParams();
  const router = useRouter();
  const { user } = useUser();
  const framework = Array.isArray(params?.framework)
    ? params?.framework[0]
    : (params?.framework as string | undefined);

  const [topic, setTopic] = useState("");
  const [taxonomy, setTaxonomy] = useState<CertificateTaxonomy | null>(null);
  const [loading, setLoading] = useState(false);
  const [intensity, setIntensity] = useState<"detailed" | "general" | "simple">("general");
  const [focusInput, setFocusInput] = useState("");
  const [focusAreas, setFocusAreas] = useState<string[]>([]);
  const [placeholderIndex, setPlaceholderIndex] = useState(0);
  const [isCreatingPlan, setIsCreatingPlan] = useState(false);

  useEffect(() => {
    let isMounted = true;
    async function loadTaxonomy() {
      if (!framework) return;
      setLoading(true);
      try {
        const data = await getCertificateTaxonomy(framework);
        if (isMounted) setTaxonomy(data);
      } finally {
        if (isMounted) setLoading(false);
      }
    }
    loadTaxonomy();
    return () => {
      isMounted = false;
    };
  }, [framework]);

  const groups = taxonomy?.groups ?? [];

  const rotatingExamples = useMemo(() => {
    if (!groups || groups.length === 0) return ["Start with your category name"];
    return groups;
  }, [groups]);

  useEffect(() => {
    if (!rotatingExamples || rotatingExamples.length <= 1) return;
    setPlaceholderIndex(0);
    const interval = setInterval(() => {
      setPlaceholderIndex((prev) => (prev + 1) % rotatingExamples.length);
    }, 2400);
    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rotatingExamples.length]);

  const handleStartLearning = async () => {
    if (!topic.trim() || !user?.uid || !framework) return;

    setIsCreatingPlan(true);

    try {
      // Call AI API to generate learning plan
      const response = await fetch('/api/ai/learning-hub', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic: topic.trim(),
          intensity,
          focusAreas: focusAreas.length > 0 ? focusAreas : undefined,
          certificateContext: taxonomy ? {
            id: framework,
            name: taxonomy.name || framework,
            provider: taxonomy.provider || 'Unknown',
            description: taxonomy.description || `${framework} certification`,
          } : undefined,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate learning plan');
      }

      const { plan } = await response.json();

      // Save the plan to the database
      const savedPlan = await createLearningPlan(user.uid, {
        ...plan,
        certificateId: framework,
        certificateName: taxonomy?.name || framework,
        certificateProvider: taxonomy?.provider || 'Unknown',
        aiModel: 'gemini-2.5-pro',
      });

      toast.success('Learning plan created successfully!');

      // Navigate to the plan page
      router.push(`/dashboard/knowledge-hub/certificates/${framework}/learning-hub/${savedPlan.id}`);

    } catch (error) {
      console.error('Error creating learning plan:', error);
      toast.error('Failed to create learning plan. Please try again.');
    } finally {
      setIsCreatingPlan(false);
    }
  };

  return (
    <div className="w-full">
      <div className="max-w-5xl mx-auto px-6 py-10">
        {/* Header */}
        <div className="text-center mb-10">
          <TextGenerateEffect
            words="What would you like to study today?"
            className="font-display"
          />
          <p className="mt-3 text-grey max-w-2xl mx-auto">
            Enter a topic name below. You can also pick from your configured categories.
          </p>
        </div>

        {/* Centered Topic Input */}
        <div className="relative">
          <div
            className={cn(
              "dtc-card rounded-2xl p-6 md:p-8 bg-gradient-to-b from-white to-white/90",
              "border border-[var(--color-border)]"
            )}
          >
            <div className="flex flex-col items-center justify-center min-h-[32vh] gap-6">
              <label
                htmlFor="topic"
                className="text-sm font-medium text-grey select-none"
              >
                Topic
              </label>
              <input
                id="topic"
                type="text"
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                placeholder={`Type a topic (e.g., ${rotatingExamples[placeholderIndex]})`}
                className={cn(
                  "w-full text-center placeholder:text-grey/70",
                  "bg-transparent outline-none",
                  "text-4xl md:text-6xl font-semibold tracking-tight text-charcoal",
                  "py-3"
                )}
                style={{ caretColor: "var(--emerald)" }}
              />
              <div className="h-px w-full bg-[var(--color-border)]" />
              <div className="flex flex-col items-center gap-5 w-full">
                <div className="text-xs text-grey">
                  Press Enter to confirm or pick a suggestion below
                </div>
                <div className="flex flex-col sm:flex-row items-center gap-3 w-full justify-center">
                  <button
                    type="button"
                    aria-label="Start Learning"
                    disabled={!topic.trim() || isCreatingPlan}
                    className={cn(
                      "inline-flex items-center gap-2 px-5 py-3 rounded-full",
                      "text-sm font-medium transition focus:outline-none",
                      "shadow-sm ring-1",
                      topic.trim() && !isCreatingPlan
                        ? "text-white ring-white/20 bg-[var(--emerald)] hover:bg-[var(--emerald-deep)]"
                        : "text-white/60 ring-white/10 bg-[color:rgb(2_108_74_/_.5)] cursor-not-allowed"
                    )}
                    onClick={handleStartLearning}
                  >
                    {isCreatingPlan ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        Creating Plan...
                      </>
                    ) : (
                      'Start Learning'
                    )}
                  </button>

                  <div
                    className={cn(
                      "inline-flex items-center gap-1 p-1 rounded-full",
                      "border border-[var(--color-border)] bg-white/70 backdrop-blur",
                      "shadow-sm"
                    )}
                    role="group"
                    aria-label="Learning depth selector"
                  >
                    {[
                      { key: "detailed", label: "Detailed" },
                      { key: "general", label: "General" },
                      { key: "simple", label: "Simple" },
                    ].map(({ key, label }) => {
                      const selected = intensity === (key as typeof intensity);
                      return (
                        <button
                          key={key}
                          type="button"
                          onClick={() => setIntensity(key as typeof intensity)}
                          className={cn(
                            "px-3.5 py-2 rounded-full text-sm transition",
                            selected
                              ? "bg-[var(--emerald)] text-white shadow"
                              : "text-charcoal hover:bg-white"
                          )}
                        >
                          {label}
                        </button>
                      );
                    })}
                  </div>
                </div>

                <div className="w-full max-w-2xl mx-auto">
                  <label className="text-xs font-medium text-grey block mb-2 select-none">Focus areas (optional)</label>
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "flex-1 flex items-center gap-2 rounded-full",
                        "border border-[var(--color-border)] bg-white/70",
                        "px-4 py-2.5"
                      )}
                    >
                      <input
                        type="text"
                        value={focusInput}
                        onChange={(e) => setFocusInput(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            const value = focusInput.trim();
                            if (!value) return;
                            const exists = focusAreas.some(
                              (fa) => fa.toLowerCase() === value.toLowerCase()
                            );
                            if (!exists) setFocusAreas((prev) => [...prev, value]);
                            setFocusInput("");
                          }
                        }}
                        placeholder="Add a focus area (e.g., Data lineage) and press Enter"
                        className="flex-1 bg-transparent outline-none text-sm"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        const value = focusInput.trim();
                        if (!value) return;
                        const exists = focusAreas.some(
                          (fa) => fa.toLowerCase() === value.toLowerCase()
                        );
                        if (!exists) setFocusAreas((prev) => [...prev, value]);
                        setFocusInput("");
                      }}
                      className={cn(
                        "inline-flex items-center justify-center rounded-full",
                        "h-10 w-10 text-sm font-medium",
                        focusInput.trim()
                          ? "bg-[var(--emerald)] text-white hover:bg-[var(--emerald-deep)]"
                          : "bg-[color:rgb(2_108_74_/_.5)] text-white/70 cursor-not-allowed"
                      )}
                      aria-label="Add focus area"
                      disabled={!focusInput.trim()}
                    >
                      +
                    </button>
                  </div>
                  {focusAreas.length > 0 && (
                    <motion.div
                      className="flex flex-wrap gap-2 mt-3"
                      initial="hidden"
                      animate="visible"
                      variants={{
                        hidden: {},
                        visible: { transition: { staggerChildren: 0.05 } },
                      }}
                    >
                      {focusAreas.map((fa) => (
                        <motion.span
                          key={fa}
                          className={cn(
                            "group inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm",
                            "bg-white border border-[var(--color-border)]"
                          )}
                          variants={{ hidden: { opacity: 0, y: 6 }, visible: { opacity: 1, y: 0 } }}
                        >
                          <span className="text-charcoal">{fa}</span>
                          <button
                            type="button"
                            aria-label={`Remove ${fa}`}
                            onClick={() =>
                              setFocusAreas((prev) => prev.filter((x) => x !== fa))
                            }
                            className="rounded-full h-5 w-5 inline-flex items-center justify-center text-grey hover:text-black hover:bg-gray-100"
                          >
                            ×
                          </button>
                        </motion.span>
                      ))}
                    </motion.div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Suggestions */}
        <div className="mt-10">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-semibold text-charcoal">Suggestions</h3>
            {loading && <span className="text-xs text-grey">Loading…</span>}
          </div>

          {groups.length === 0 ? (
            <div className="text-grey text-sm">
              No categories configured yet for this certificate.
            </div>
          ) : (
            <motion.div
              className="flex flex-wrap gap-2"
              initial="hidden"
              animate="visible"
              variants={{
                hidden: {},
                visible: {
                  transition: { staggerChildren: 0.05 },
                },
              }}
            >
              {groups.map((g) => (
                <motion.button
                  key={g}
                  type="button"
                  onClick={() => setTopic(g)}
                  className={cn(
                    "px-3.5 py-2 rounded-full text-sm",
                    "bg-white text-charcoal border border-[var(--color-border)]",
                    "hover:border-[var(--emerald)] hover:text-black transition",
                    topic === g ? "ring-2 ring-[var(--emerald)]" : ""
                  )}
                  variants={{ hidden: { opacity: 0, y: 6 }, visible: { opacity: 1, y: 0 } }}
                  whileTap={{ scale: 0.98 }}
                >
                  {g}
                </motion.button>
              ))}
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
}
