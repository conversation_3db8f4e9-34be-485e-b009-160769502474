"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, CheckCircle, RotateCcw } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { type LearningTool, type LearningPlan } from "@/Services/planService";
import { DtcHero } from "@/components/ui/Hero";

// Import learning tool components
import Flashcards from "./tools/Flashcards";
import TrueOrFalse from "./tools/TrueOrFalse";
import TwoFactsOneLie from "./tools/TwoFactsOneLie";
import KnowledgeGraphs from "./tools/KnowledgeGraphs";
import PracticalScenarioGuidance from "./tools/PracticalScenarioGuidance";
import MatchConcepts from "./tools/MatchConcepts";
import ConceptsGuidedLearning from "./tools/ConceptsGuidedLearning";

interface LearningLessonProps {
  tool: LearningTool;
  plan: LearningPlan;
  onComplete: (toolId: string, progress: number, completed: boolean) => Promise<void>;
  onBack: () => void;
}

export default function LearningLesson({ tool, plan, onComplete, onBack }: LearningLessonProps) {
  const [progress, setProgress] = useState(tool.progress || 0);
  const [isCompleted, setIsCompleted] = useState(tool.completed || false);
  const [isLoading, setIsLoading] = useState(false);
  const [aiEnhancing, setAiEnhancing] = useState(false);

  useEffect(() => {
    setProgress(tool.progress || 0);
    setIsCompleted(tool.completed || false);
  }, [tool]);

  const handleProgressUpdate = async (newProgress: number, completed: boolean = false) => {
    setProgress(newProgress);
    setIsCompleted(completed);
    
    try {
      setIsLoading(true);
      await onComplete(tool.id, newProgress, completed);
    } catch (error) {
      console.error('Error updating progress:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAIEnhancement = async () => {
    setAiEnhancing(true);
    
    try {
      // Call AI enhancement API
      const response = await fetch('/api/ai/learning-lesson', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          toolType: tool.type,
          toolContent: tool.content,
          topic: plan.topic,
          userProgress: progress,
          difficulty: plan.difficulty,
        }),
      });

      if (response.ok) {
        const { enhancedContent } = await response.json();
        // Update tool content with enhanced version
        tool.content = enhancedContent;
      }
    } catch (error) {
      console.error('Error enhancing content:', error);
    } finally {
      setAiEnhancing(false);
    }
  };

  const renderToolComponent = () => {
    const commonProps = {
      content: tool.content,
      onProgressUpdate: handleProgressUpdate,
      progress,
      isCompleted,
    };

    switch (tool.type) {
      case 'flashcards':
        return <Flashcards {...commonProps} />;
      case 'trueOrFalse':
        return <TrueOrFalse {...commonProps} />;
      case 'twoFactsOneLie':
        return <TwoFactsOneLie {...commonProps} />;
      case 'knowledgeGraphs':
        return <KnowledgeGraphs {...commonProps} />;
      case 'practicalScenarioGuidance':
        return <PracticalScenarioGuidance {...commonProps} />;
      case 'matchConcepts':
        return <MatchConcepts {...commonProps} />;
      case 'conceptsGuidedLearning':
        return <ConceptsGuidedLearning {...commonProps} />;
      default:
        return (
          <div className="text-center py-20">
            <p className="text-[var(--grey)]">Tool type not implemented yet: {tool.type}</p>
          </div>
        );
    }
  };

  const getToolIcon = (type: string) => {
    switch (type) {
      case 'flashcards':
        return '🃏';
      case 'trueOrFalse':
        return '✅';
      case 'twoFactsOneLie':
        return '🎯';
      case 'knowledgeGraphs':
        return '🕸️';
      case 'practicalScenarioGuidance':
        return '🎭';
      case 'matchConcepts':
        return '🔗';
      case 'conceptsGuidedLearning':
        return '📚';
      default:
        return '📖';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-[var(--color-muted)] to-white/50">
      {/* Hero Section */}
      <DtcHero
        title={
          <div className="flex items-center gap-3">
            <span className="text-4xl">{getToolIcon(tool.type)}</span>
            {tool.name}
          </div>
        }
        subtitle={tool.description}
        breadcrumbs={[
          { label: "Knowledge Hub", href: "/dashboard/knowledge-hub" },
          { label: plan.certificateId, href: `/dashboard/knowledge-hub/certificates/${plan.certificateId}` },
          { label: "Learning Hub", href: `/dashboard/knowledge-hub/certificates/${plan.certificateId}/learning-hub` },
          { label: plan.title, href: `/dashboard/knowledge-hub/certificates/${plan.certificateId}/learning-hub/${plan.id}` },
          { label: tool.name, href: "#" }
        ]}
      />

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Header Controls */}
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="outline"
            onClick={onBack}
            className="text-[var(--charcoal)] border-[var(--color-border)]"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Plan
          </Button>

          <div className="flex items-center gap-4">
            {/* AI Enhancement Button */}
            <Button
              variant="outline"
              onClick={handleAIEnhancement}
              disabled={aiEnhancing}
              className="text-[var(--emerald)] border-[var(--emerald)] hover:bg-[var(--emerald)]/10"
            >
              {aiEnhancing ? (
                <>
                  <RotateCcw className="w-4 h-4 mr-2 animate-spin" />
                  Enhancing...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  AI Enhance
                </>
              )}
            </Button>

            {/* Progress Indicator */}
            <div className="flex items-center gap-2 text-sm text-[var(--grey)]">
              {isCompleted ? (
                <>
                  <CheckCircle className="w-5 h-5 text-[var(--emerald)]" />
                  Completed
                </>
              ) : (
                <>
                  <div className="w-5 h-5 rounded-full border-2 border-[var(--emerald)] relative">
                    <div 
                      className="absolute inset-0 rounded-full bg-[var(--emerald)] transition-all duration-300"
                      style={{ 
                        clipPath: `polygon(50% 50%, 50% 0%, ${50 + (progress / 100) * 50}% 0%, ${50 + (progress / 100) * 50}% 100%, 50% 100%)`,
                        transform: `rotate(${(progress / 100) * 360}deg)`
                      }}
                    />
                  </div>
                  {progress}% Complete
                </>
              )}
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-[var(--color-border)] mb-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-[var(--charcoal)]">Learning Progress</h3>
            <span className="text-xl font-bold text-[var(--emerald)]">{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <motion.div 
              className="bg-gradient-to-r from-[var(--emerald)] to-[var(--bright-green)] h-3 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
          <div className="flex justify-between text-sm text-[var(--grey)] mt-2">
            <span>Estimated time: {tool.estimatedTime}</span>
            <span>{isCompleted ? 'Completed!' : 'In Progress'}</span>
          </div>
        </div>

        {/* Tool Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={tool.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-2xl shadow-sm border border-[var(--color-border)] overflow-hidden"
          >
            {renderToolComponent()}
          </motion.div>
        </AnimatePresence>

        {/* Completion Celebration */}
        <AnimatePresence>
          {isCompleted && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
              onClick={onBack}
            >
              <motion.div
                initial={{ y: 50 }}
                animate={{ y: 0 }}
                className="bg-white rounded-3xl p-8 text-center max-w-md mx-4"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="text-6xl mb-4">🎉</div>
                <h3 className="text-2xl font-bold text-[var(--charcoal)] mb-2">
                  Tool Completed!
                </h3>
                <p className="text-[var(--grey)] mb-6">
                  Great job! You've successfully completed {tool.name}.
                </p>
                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    onClick={onBack}
                    className="flex-1"
                  >
                    Back to Plan
                  </Button>
                  <Button
                    onClick={() => handleProgressUpdate(0, false)}
                    className="flex-1 bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Retry
                  </Button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
