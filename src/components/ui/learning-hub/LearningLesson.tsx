"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Arrow<PERSON>ef<PERSON>, ArrowRight, CheckCircle2, Clock, Target, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { type LearningPlan, type LearningTool, completeToolInPlan, updatePlanProgress } from "@/Services/planService";
import { useUser } from "@/hooks/useUser";

// Import learning tool components
import Flashcards from "./tools/Flashcards";
import TrueOrFalse from "./tools/TrueOrFalse";
import TwoFactsOneLie from "./tools/TwoFactsOneLie";
import KnowledgeGraphs from "./tools/KnowledgeGraphs";
import PracticalScenarioGuidance from "./tools/PracticalScenarioGuidance";
import MatchConcepts from "./tools/MatchConcepts";
import ConceptsGuidedLearning from "./tools/ConceptsGuidedLearning";

interface LearningLessonProps {
  plan: LearningPlan;
  onBack: () => void;
  onPlanUpdate: (plan: LearningPlan) => void;
}

export default function LearningLesson({ plan, onBack, onPlanUpdate }: LearningLessonProps) {
  const { user } = useUser();
  const [currentToolIndex, setCurrentToolIndex] = useState(plan.currentToolIndex);
  const [completedTools, setCompletedTools] = useState(new Set(plan.tools.filter(t => t.completed).map(t => t.id)));
  const [toolStartTime, setToolStartTime] = useState<Date>(new Date());

  const currentTool = plan.tools[currentToolIndex];
  const isLastTool = currentToolIndex === plan.tools.length - 1;
  const canGoNext = completedTools.has(currentTool?.id) || currentToolIndex < plan.currentToolIndex;
  const canGoPrevious = currentToolIndex > 0;

  useEffect(() => {
    setToolStartTime(new Date());
  }, [currentToolIndex]);

  const handleToolComplete = async (toolId: string, score?: number) => {
    if (!user?.uid) return;

    const timeSpent = Math.round((new Date().getTime() - toolStartTime.getTime()) / 60000); // minutes
    
    try {
      await completeToolInPlan(user.uid, plan.id, toolId, score, timeSpent);
      
      // Update local state
      setCompletedTools(prev => new Set([...prev, toolId]));
      
      // Update plan progress
      const newCompletedCount = completedTools.size + 1;
      const newProgressPercentage = Math.round((newCompletedCount / plan.tools.length) * 100);
      const newCurrentIndex = Math.min(currentToolIndex + 1, plan.tools.length - 1);
      
      const updatedPlan: LearningPlan = {
        ...plan,
        completedTools: newCompletedCount,
        progressPercentage: newProgressPercentage,
        currentToolIndex: newCurrentIndex,
        status: newCompletedCount === plan.tools.length ? 'completed' : plan.status,
        tools: plan.tools.map(tool => 
          tool.id === toolId 
            ? { ...tool, completed: true, completedAt: new Date(), score, attempts: (tool.attempts || 0) + 1 }
            : tool
        )
      };
      
      onPlanUpdate(updatedPlan);
      
      // Auto-advance to next tool if not last
      if (!isLastTool) {
        setTimeout(() => {
          setCurrentToolIndex(prev => prev + 1);
        }, 1500);
      }
    } catch (error) {
      console.error('Error completing tool:', error);
    }
  };

  const handleNextTool = () => {
    if (canGoNext && currentToolIndex < plan.tools.length - 1) {
      setCurrentToolIndex(prev => prev + 1);
    }
  };

  const handlePreviousTool = () => {
    if (canGoPrevious) {
      setCurrentToolIndex(prev => prev - 1);
    }
  };

  const renderTool = (tool: LearningTool) => {
    const commonProps = {
      tool,
      onComplete: (score?: number) => handleToolComplete(tool.id, score),
      isCompleted: completedTools.has(tool.id),
    };

    switch (tool.type) {
      case 'flashcards':
        return <Flashcards {...commonProps} />;
      case 'trueOrFalse':
        return <TrueOrFalse {...commonProps} />;
      case 'twoFactsOneLie':
        return <TwoFactsOneLie {...commonProps} />;
      case 'knowledgeGraphs':
        return <KnowledgeGraphs {...commonProps} />;
      case 'practicalScenarioGuidance':
        return <PracticalScenarioGuidance {...commonProps} />;
      case 'matchConcepts':
        return <MatchConcepts {...commonProps} />;
      case 'conceptsGuidedLearning':
        return <ConceptsGuidedLearning {...commonProps} />;
      default:
        return (
          <div className="text-center py-12">
            <p className="text-[var(--grey)]">Tool type "{tool.type}" not implemented yet.</p>
          </div>
        );
    }
  };

  if (!currentTool) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-[var(--color-muted)] to-white/50 flex items-center justify-center">
        <div className="text-center">
          <CheckCircle2 className="w-16 h-16 text-[var(--emerald)] mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-[var(--charcoal)] mb-2">Congratulations!</h2>
          <p className="text-[var(--grey)] mb-6">You've completed all learning tools in this plan.</p>
          <Button onClick={onBack} className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)]">
            Back to Overview
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-[var(--color-muted)] to-white/50">
      {/* Header */}
      <div className="bg-white border-b border-[var(--color-border)] sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                onClick={onBack}
                variant="ghost"
                size="sm"
                className="text-[var(--grey)] hover:text-[var(--charcoal)]"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Overview
              </Button>
              <div className="h-6 w-px bg-[var(--color-border)]" />
              <div>
                <h1 className="font-semibold text-[var(--charcoal)]">{plan.title}</h1>
                <p className="text-sm text-[var(--grey)]">
                  Tool {currentToolIndex + 1} of {plan.tools.length}: {currentTool.name}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm text-[var(--grey)]">
                <Clock className="w-4 h-4" />
                {currentTool.estimatedTime}
              </div>
              <div className="flex items-center gap-2 text-sm text-[var(--grey)]">
                <Target className="w-4 h-4" />
                {completedTools.size}/{plan.tools.length} Complete
              </div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs text-[var(--grey)]">Overall Progress</span>
              <span className="text-xs text-[var(--grey)]">{Math.round((completedTools.size / plan.tools.length) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5">
              <motion.div
                className="bg-[var(--emerald)] h-1.5 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(completedTools.size / plan.tools.length) * 100}%` }}
                transition={{ duration: 0.5, ease: "easeOut" }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Tool Navigation */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center gap-2">
            {plan.tools.map((tool, index) => (
              <button
                key={tool.id}
                onClick={() => setCurrentToolIndex(index)}
                disabled={index > plan.currentToolIndex && !completedTools.has(tool.id)}
                className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium transition-all",
                  index === currentToolIndex
                    ? "bg-[var(--emerald)] text-white scale-110"
                    : completedTools.has(tool.id)
                    ? "bg-[var(--bright-green)] text-white hover:scale-105"
                    : index <= plan.currentToolIndex
                    ? "bg-gray-200 text-[var(--grey)] hover:bg-gray-300"
                    : "bg-gray-100 text-gray-400 cursor-not-allowed"
                )}
              >
                {completedTools.has(tool.id) ? '✓' : index + 1}
              </button>
            ))}
          </div>
        </div>

        {/* Tool Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentTool.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="max-w-4xl mx-auto"
          >
            {renderTool(currentTool)}
          </motion.div>
        </AnimatePresence>

        {/* Navigation Buttons */}
        <div className="flex items-center justify-between mt-8 max-w-4xl mx-auto">
          <Button
            onClick={handlePreviousTool}
            disabled={!canGoPrevious}
            variant="outline"
            className={cn(
              "flex items-center gap-2",
              !canGoPrevious && "opacity-50 cursor-not-allowed"
            )}
          >
            <ArrowLeft className="w-4 h-4" />
            Previous Tool
          </Button>

          <div className="text-center">
            <p className="text-sm text-[var(--grey)]">
              {currentTool.name}
            </p>
            <p className="text-xs text-[var(--grey)]">
              {currentTool.estimatedTime}
            </p>
          </div>

          <Button
            onClick={handleNextTool}
            disabled={!canGoNext}
            className={cn(
              "flex items-center gap-2 bg-[var(--emerald)] hover:bg-[var(--emerald-deep)]",
              !canGoNext && "opacity-50 cursor-not-allowed"
            )}
          >
            {isLastTool ? 'Complete Plan' : 'Next Tool'}
            <ArrowRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
