"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronRight, ChevronLeft, CheckCircle, AlertTriangle, Lightbulb, Target, RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ScenarioStep {
  id: string;
  title: string;
  description: string;
  options: {
    id: string;
    text: string;
    isCorrect: boolean;
    feedback: string;
    consequence?: string;
  }[];
  guidance?: string;
}

interface Scenario {
  id: string;
  title: string;
  description: string;
  context: string;
  steps: ScenarioStep[];
  difficulty?: "easy" | "medium" | "hard";
  category?: string;
}

interface PracticalScenarioGuidanceProps {
  content: {
    scenarios: Scenario[];
    title?: string;
    description?: string;
  };
  onProgressUpdate: (progress: number, completed?: boolean) => void;
  progress: number;
  isCompleted: boolean;
}

export default function PracticalScenarioGuidance({ content, onProgressUpdate, progress, isCompleted }: PracticalScenarioGuidanceProps) {
  const [currentScenarioIndex, setCurrentScenarioIndex] = useState(0);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState<Map<string, string>>(new Map());
  const [showFeedback, setShowFeedback] = useState(false);
  const [completedScenarios, setCompletedScenarios] = useState<Set<number>>(new Set());
  const [scenarioScore, setScenarioScore] = useState(0);

  const scenarios = content?.scenarios || [];
  const currentScenario = scenarios[currentScenarioIndex];
  const currentStep = currentScenario?.steps[currentStepIndex];

  useEffect(() => {
    if (completedScenarios.size > 0) {
      const newProgress = Math.round((completedScenarios.size / scenarios.length) * 100);
      const completed = completedScenarios.size === scenarios.length;
      onProgressUpdate(newProgress, completed);
    }
  }, [completedScenarios, scenarios.length, onProgressUpdate]);

  const handleOptionSelect = (optionId: string) => {
    const stepKey = `${currentScenarioIndex}-${currentStepIndex}`;
    setSelectedOptions(prev => new Map(prev.set(stepKey, optionId)));
    setShowFeedback(true);
  };

  const handleNextStep = () => {
    if (currentStepIndex < currentScenario.steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
      setShowFeedback(false);
    } else {
      // Scenario completed
      calculateScenarioScore();
      setCompletedScenarios(prev => new Set([...prev, currentScenarioIndex]));
    }
  };

  const handlePreviousStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
      setShowFeedback(false);
    }
  };

  const handleNextScenario = () => {
    if (currentScenarioIndex < scenarios.length - 1) {
      setCurrentScenarioIndex(currentScenarioIndex + 1);
      setCurrentStepIndex(0);
      setShowFeedback(false);
      setScenarioScore(0);
    }
  };

  const handlePreviousScenario = () => {
    if (currentScenarioIndex > 0) {
      setCurrentScenarioIndex(currentScenarioIndex - 1);
      setCurrentStepIndex(0);
      setShowFeedback(false);
      setScenarioScore(0);
    }
  };

  const calculateScenarioScore = () => {
    let correctAnswers = 0;
    currentScenario.steps.forEach((step, stepIndex) => {
      const stepKey = `${currentScenarioIndex}-${stepIndex}`;
      const selectedOptionId = selectedOptions.get(stepKey);
      const selectedOption = step.options.find(opt => opt.id === selectedOptionId);
      if (selectedOption?.isCorrect) {
        correctAnswers++;
      }
    });
    setScenarioScore(Math.round((correctAnswers / currentScenario.steps.length) * 100));
  };

  const handleReset = () => {
    setCurrentScenarioIndex(0);
    setCurrentStepIndex(0);
    setSelectedOptions(new Map());
    setShowFeedback(false);
    setCompletedScenarios(new Set());
    setScenarioScore(0);
    onProgressUpdate(0, false);
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'hard':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (scenarios.length === 0) {
    return (
      <div className="p-8 text-center">
        <p className="text-[var(--grey)]">No practical scenarios available for this lesson.</p>
      </div>
    );
  }

  const stepKey = `${currentScenarioIndex}-${currentStepIndex}`;
  const selectedOptionId = selectedOptions.get(stepKey);
  const selectedOption = currentStep?.options.find(opt => opt.id === selectedOptionId);
  const isScenarioCompleted = completedScenarios.has(currentScenarioIndex);

  return (
    <div className="p-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--charcoal)] mb-2">
          {content.title || "Practical Scenario Guidance"}
        </h2>
        {content.description && (
          <p className="text-[var(--grey)] max-w-2xl mx-auto">
            {content.description}
          </p>
        )}
      </div>

      {/* Scenario Navigation */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={handlePreviousScenario}
            disabled={currentScenarioIndex === 0}
            className="text-[var(--charcoal)] border-[var(--color-border)]"
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Previous Scenario
          </Button>
          <Button
            variant="outline"
            onClick={handleReset}
            className="text-[var(--grey)] border-[var(--color-border)]"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset All
          </Button>
        </div>

        <div className="flex items-center gap-4 text-sm text-[var(--grey)]">
          <span>Scenario {currentScenarioIndex + 1} of {scenarios.length}</span>
          <span>•</span>
          <span>Step {currentStepIndex + 1} of {currentScenario?.steps.length}</span>
          {isScenarioCompleted && (
            <>
              <span>•</span>
              <div className="flex items-center gap-1 text-[var(--emerald)]">
                <CheckCircle className="w-4 h-4" />
                Complete ({scenarioScore}%)
              </div>
            </>
          )}
        </div>

        <Button
          variant="outline"
          onClick={handleNextScenario}
          disabled={currentScenarioIndex === scenarios.length - 1}
          className="text-[var(--charcoal)] border-[var(--color-border)]"
        >
          Next Scenario
          <ChevronRight className="w-4 h-4 ml-2" />
        </Button>
      </div>

      {/* Scenario Progress */}
      <div className="max-w-4xl mx-auto mb-8">
        <div className="flex justify-center mb-4">
          <div className="flex gap-2">
            {scenarios.map((_, index) => (
              <div
                key={index}
                className={cn(
                  "w-3 h-3 rounded-full transition-all duration-200",
                  index === currentScenarioIndex
                    ? "bg-[var(--emerald)] scale-125"
                    : completedScenarios.has(index)
                    ? "bg-[var(--bright-green)]"
                    : "bg-gray-200"
                )}
              />
            ))}
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-[var(--emerald)] h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStepIndex + 1) / currentScenario.steps.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Scenario Content */}
      <div className="max-w-4xl mx-auto">
        {/* Scenario Header */}
        <div className="bg-white rounded-2xl p-8 shadow-lg border border-[var(--color-border)] mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-2xl font-bold text-[var(--charcoal)]">
              {currentScenario.title}
            </h3>
            <div className="flex items-center gap-3">
              {currentScenario.difficulty && (
                <div className={cn(
                  "px-3 py-1 rounded-full text-xs font-medium border",
                  getDifficultyColor(currentScenario.difficulty)
                )}>
                  {currentScenario.difficulty}
                </div>
              )}
              {currentScenario.category && (
                <div className="px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                  {currentScenario.category}
                </div>
              )}
            </div>
          </div>
          
          <p className="text-[var(--grey)] mb-4 leading-relaxed">
            {currentScenario.description}
          </p>
          
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-blue-800 mb-1">Context</div>
                <div className="text-blue-700 text-sm leading-relaxed">
                  {currentScenario.context}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Current Step */}
        <AnimatePresence mode="wait">
          <motion.div
            key={`${currentScenarioIndex}-${currentStepIndex}`}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-2xl p-8 shadow-lg border border-[var(--color-border)] mb-6"
          >
            <div className="flex items-start gap-3 mb-6">
              <div className="w-8 h-8 rounded-full bg-[var(--emerald)] text-white flex items-center justify-center font-semibold">
                {currentStepIndex + 1}
              </div>
              <div className="flex-1">
                <h4 className="text-xl font-semibold text-[var(--charcoal)] mb-2">
                  {currentStep.title}
                </h4>
                <p className="text-[var(--grey)] leading-relaxed">
                  {currentStep.description}
                </p>
              </div>
            </div>

            {/* Options */}
            {!showFeedback ? (
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Target className="w-5 h-5 text-[var(--emerald)]" />
                  <span className="font-medium text-[var(--charcoal)]">What would you do?</span>
                </div>
                {currentStep.options.map((option, index) => (
                  <motion.button
                    key={option.id}
                    onClick={() => handleOptionSelect(option.id)}
                    className={cn(
                      "w-full p-6 text-left rounded-xl border-2 transition-all duration-200",
                      "hover:border-[var(--emerald)] hover:bg-[var(--emerald)]/5",
                      "focus:outline-none focus:ring-2 focus:ring-[var(--emerald)]/50"
                    )}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 rounded-full bg-[var(--emerald)]/10 flex items-center justify-center text-[var(--emerald)] font-semibold">
                        {String.fromCharCode(65 + index)}
                      </div>
                      <div className="text-[var(--charcoal)] leading-relaxed">
                        {option.text}
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
            ) : (
              /* Feedback */
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-4"
              >
                {currentStep.options.map((option, index) => {
                  const isSelected = option.id === selectedOptionId;
                  const isCorrect = option.isCorrect;
                  
                  return (
                    <div
                      key={option.id}
                      className={cn(
                        "p-6 rounded-xl border-2",
                        isSelected && isCorrect && "border-green-500 bg-green-50",
                        isSelected && !isCorrect && "border-red-500 bg-red-50",
                        !isSelected && isCorrect && "border-green-300 bg-green-25",
                        !isSelected && !isCorrect && "border-gray-200 bg-gray-50"
                      )}
                    >
                      <div className="flex items-start gap-4">
                        <div className={cn(
                          "w-8 h-8 rounded-full flex items-center justify-center font-semibold",
                          isSelected && isCorrect && "bg-green-100 text-green-600",
                          isSelected && !isCorrect && "bg-red-100 text-red-600",
                          !isSelected && isCorrect && "bg-green-100 text-green-600",
                          !isSelected && !isCorrect && "bg-gray-100 text-gray-600"
                        )}>
                          {isCorrect ? <CheckCircle className="w-5 h-5" /> : String.fromCharCode(65 + index)}
                        </div>
                        <div className="flex-1">
                          <div className={cn(
                            "font-medium mb-2",
                            isSelected && isCorrect && "text-green-800",
                            isSelected && !isCorrect && "text-red-800",
                            !isSelected && isCorrect && "text-green-700",
                            !isSelected && !isCorrect && "text-gray-700"
                          )}>
                            {option.text} {isSelected && "(Your Choice)"}
                          </div>
                          <div className={cn(
                            "text-sm leading-relaxed",
                            isSelected && isCorrect && "text-green-700",
                            isSelected && !isCorrect && "text-red-700",
                            !isSelected && isCorrect && "text-green-600",
                            !isSelected && !isCorrect && "text-gray-600"
                          )}>
                            {option.feedback}
                          </div>
                          {option.consequence && (
                            <div className={cn(
                              "text-sm mt-2 p-3 rounded-lg",
                              isCorrect ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"
                            )}>
                              <strong>Consequence:</strong> {option.consequence}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}

                {/* Guidance */}
                {currentStep.guidance && (
                  <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                    <div className="flex items-start gap-3">
                      <Lightbulb className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="font-medium text-blue-800 mb-2">Expert Guidance</div>
                        <div className="text-blue-700 text-sm leading-relaxed">
                          {currentStep.guidance}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Navigation */}
        {showFeedback && (
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={handlePreviousStep}
              disabled={currentStepIndex === 0}
              className="text-[var(--charcoal)] border-[var(--color-border)]"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous Step
            </Button>

            {currentStepIndex < currentScenario.steps.length - 1 ? (
              <Button
                onClick={handleNextStep}
                className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
              >
                Next Step
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Button
                onClick={handleNextStep}
                className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
              >
                Complete Scenario
                <CheckCircle className="w-4 h-4 ml-2" />
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Completion Message */}
      {isCompleted && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mt-8 p-6 bg-gradient-to-r from-[var(--emerald)]/10 to-[var(--bright-green)]/10 rounded-2xl border border-[var(--emerald)]/20"
        >
          <div className="text-4xl mb-4">🎭</div>
          <h3 className="text-xl font-semibold text-[var(--charcoal)] mb-2">
            All Scenarios Completed!
          </h3>
          <p className="text-[var(--grey)]">
            You've successfully navigated through all {scenarios.length} practical scenarios.
          </p>
        </motion.div>
      )}
    </div>
  );
}
