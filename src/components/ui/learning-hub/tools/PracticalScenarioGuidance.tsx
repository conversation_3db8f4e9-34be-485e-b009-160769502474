"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CheckCircle2, X, ArrowRight, RotateCcw, Lightbulb, AlertTriangle, Target } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { type LearningTool } from "@/Services/planService";

interface ScenarioStep {
  id: string;
  situation: string;
  options: {
    id: string;
    text: string;
    isCorrect: boolean;
    explanation: string;
    consequences?: string;
  }[];
  context?: string;
  hints?: string[];
}

interface Scenario {
  id: string;
  title: string;
  description: string;
  context: string;
  steps: ScenarioStep[];
  learningOutcomes: string[];
}

interface PracticalScenarioGuidanceProps {
  tool: LearningTool;
  onComplete: (score?: number) => void;
  isCompleted: boolean;
}

export default function PracticalScenarioGuidance({ tool, onComplete, isCompleted }: PracticalScenarioGuidanceProps) {
  const scenarios: Scenario[] = tool.content?.scenarios || [];
  
  const [currentScenarioIndex, setCurrentScenarioIndex] = useState(0);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);
  const [decisions, setDecisions] = useState<{ [key: string]: string }>({});
  const [showHints, setShowHints] = useState(false);
  const [startTime, setStartTime] = useState<Date>(new Date());

  const currentScenario = scenarios[currentScenarioIndex];
  const currentStep = currentScenario?.steps[currentStepIndex];
  const totalScenarios = scenarios.length;
  const totalSteps = currentScenario?.steps.length || 0;
  
  const completedDecisions = Object.keys(decisions).length;
  const correctDecisions = Object.entries(decisions).filter(([stepId, optionId]) => {
    // Find the step and option to check if it's correct
    for (const scenario of scenarios) {
      for (const step of scenario.steps) {
        if (step.id === stepId) {
          const option = step.options.find(opt => opt.id === optionId);
          return option?.isCorrect;
        }
      }
    }
    return false;
  }).length;

  const accuracy = completedDecisions > 0 ? Math.round((correctDecisions / completedDecisions) * 100) : 0;

  useEffect(() => {
    setStartTime(new Date());
  }, []);

  const handleOptionSelect = (optionId: string) => {
    if (showExplanation) return;
    
    setSelectedOption(optionId);
    setShowExplanation(true);
    
    // Record the decision
    setDecisions(prev => ({
      ...prev,
      [currentStep.id]: optionId
    }));
  };

  const handleNext = () => {
    if (currentStepIndex < totalSteps - 1) {
      // Next step in current scenario
      setCurrentStepIndex(prev => prev + 1);
      setSelectedOption(null);
      setShowExplanation(false);
      setShowHints(false);
    } else if (currentScenarioIndex < totalScenarios - 1) {
      // Next scenario
      setCurrentScenarioIndex(prev => prev + 1);
      setCurrentStepIndex(0);
      setSelectedOption(null);
      setShowExplanation(false);
      setShowHints(false);
    } else {
      // Completed all scenarios
      const finalScore = Math.round((correctDecisions / completedDecisions) * 100);
      onComplete(finalScore);
    }
  };

  const handleReset = () => {
    setCurrentScenarioIndex(0);
    setCurrentStepIndex(0);
    setSelectedOption(null);
    setShowExplanation(false);
    setDecisions({});
    setShowHints(false);
    setStartTime(new Date());
  };

  const toggleHints = () => {
    setShowHints(!showHints);
  };

  if (totalScenarios === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <X className="w-8 h-8 text-red-600" />
        </div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">No Scenarios Available</h3>
        <p className="text-[var(--grey)]">This tool doesn't have any practical scenarios configured.</p>
      </div>
    );
  }

  if (isCompleted) {
    const timeSpent = Math.round((new Date().getTime() - startTime.getTime()) / 60000);
    
    return (
      <div className="text-center py-12">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="w-16 h-16 bg-[var(--emerald)]/10 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <CheckCircle2 className="w-8 h-8 text-[var(--emerald)]" />
        </motion.div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">Scenarios Completed!</h3>
        <p className="text-[var(--grey)] mb-6">
          You made {correctDecisions} out of {completedDecisions} correct decisions ({accuracy}% accuracy)
          {timeSpent > 0 && ` in ${timeSpent} minutes`}.
        </p>
        
        <div className="grid grid-cols-3 gap-4 max-w-md mx-auto mb-6">
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--emerald)]">{correctDecisions}</div>
            <div className="text-sm text-[var(--grey)]">Correct</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-red-500">{completedDecisions - correctDecisions}</div>
            <div className="text-sm text-[var(--grey)]">Incorrect</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--bright-green)]">{accuracy}%</div>
            <div className="text-sm text-[var(--grey)]">Accuracy</div>
          </div>
        </div>
        
        <Button onClick={handleReset} variant="outline">
          <RotateCcw className="w-4 h-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  const selectedOptionData = currentStep?.options.find(opt => opt.id === selectedOption);
  const isCorrect = selectedOptionData?.isCorrect;

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-3xl font-bold text-[var(--charcoal)] mb-2"
        >
          {tool.name}
        </motion.h2>
        <p className="text-[var(--grey)] text-lg">{tool.description}</p>
      </div>

      {/* Progress */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-[var(--charcoal)]">
            Scenario {currentScenarioIndex + 1} of {totalScenarios} • Step {currentStepIndex + 1} of {totalSteps}
          </span>
          <div className="flex items-center gap-4 text-sm text-[var(--grey)]">
            <div className="flex items-center gap-1">
              <Target className="w-4 h-4" />
              {accuracy}% accuracy
            </div>
            <div className="flex items-center gap-1">
              <CheckCircle2 className="w-4 h-4" />
              {correctDecisions}/{completedDecisions} correct
            </div>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className="bg-[var(--emerald)] h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${((currentScenarioIndex * totalSteps + currentStepIndex + 1) / (totalScenarios * totalSteps)) * 100}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      {/* Scenario Context */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-[var(--emerald)]/10 to-[var(--bright-green)]/10 rounded-2xl p-6 mb-8 border border-[var(--emerald)]/20"
      >
        <h3 className="text-xl font-semibold text-[var(--charcoal)] mb-2">{currentScenario?.title}</h3>
        <p className="text-[var(--grey)] mb-4">{currentScenario?.description}</p>
        <div className="bg-white/50 rounded-lg p-4">
          <h4 className="font-medium text-[var(--charcoal)] mb-2">Context:</h4>
          <p className="text-[var(--grey)] text-sm leading-relaxed">{currentScenario?.context}</p>
        </div>
      </motion.div>

      {/* Current Step */}
      <AnimatePresence mode="wait">
        <motion.div
          key={`${currentScenarioIndex}-${currentStepIndex}`}
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -50 }}
          transition={{ duration: 0.3 }}
          className="bg-white rounded-2xl p-8 shadow-lg border border-[var(--color-border)] mb-8"
        >
          {/* Situation */}
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-3">
              <AlertTriangle className="w-5 h-5 text-[var(--emerald)]" />
              <h4 className="font-semibold text-[var(--charcoal)]">Situation</h4>
            </div>
            <p className="text-[var(--grey)] leading-relaxed">{currentStep?.situation}</p>
            
            {currentStep?.context && (
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">{currentStep.context}</p>
              </div>
            )}
          </div>

          {/* Hints */}
          {currentStep?.hints && currentStep.hints.length > 0 && (
            <div className="mb-6">
              <Button
                onClick={toggleHints}
                variant="outline"
                size="sm"
                className="mb-3"
              >
                <Lightbulb className="w-4 h-4 mr-2" />
                {showHints ? 'Hide Hints' : 'Show Hints'}
              </Button>
              
              <AnimatePresence>
                {showHints && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="bg-yellow-50 rounded-lg p-4"
                  >
                    <ul className="space-y-2">
                      {currentStep.hints.map((hint, index) => (
                        <li key={index} className="flex items-start gap-2 text-sm text-yellow-800">
                          <Lightbulb className="w-4 h-4 mt-0.5 flex-shrink-0" />
                          {hint}
                        </li>
                      ))}
                    </ul>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}

          {/* Options */}
          {!showExplanation && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <h4 className="font-semibold text-[var(--charcoal)] mb-4">What would you do?</h4>
              {currentStep?.options.map((option, index) => (
                <Button
                  key={option.id}
                  onClick={() => handleOptionSelect(option.id)}
                  variant="outline"
                  className={cn(
                    "w-full p-6 h-auto text-left justify-start",
                    "border-2 hover:border-[var(--emerald)] hover:bg-[var(--emerald)]/5",
                    "transform hover:scale-[1.02] transition-all duration-200"
                  )}
                >
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 rounded-full bg-[var(--emerald)]/10 text-[var(--emerald)] flex items-center justify-center font-semibold text-sm">
                      {String.fromCharCode(65 + index)}
                    </div>
                    <div className="flex-1 text-[var(--charcoal)] leading-relaxed">
                      {option.text}
                    </div>
                  </div>
                </Button>
              ))}
            </motion.div>
          )}

          {/* Explanation */}
          {showExplanation && selectedOptionData && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              {/* Result */}
              <div className={cn(
                "flex items-center justify-center gap-3 p-4 rounded-xl",
                isCorrect 
                  ? "bg-[var(--emerald)]/10 text-[var(--emerald)]" 
                  : "bg-red-50 text-red-600"
              )}>
                {isCorrect ? (
                  <CheckCircle2 className="w-6 h-6" />
                ) : (
                  <X className="w-6 h-6" />
                )}
                <span className="font-semibold text-lg">
                  {isCorrect ? 'Good Decision!' : 'Consider This...'}
                </span>
              </div>

              {/* Explanation */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h4 className="font-semibold text-[var(--charcoal)] mb-2">Explanation:</h4>
                <p className="text-[var(--grey)] leading-relaxed mb-4">{selectedOptionData.explanation}</p>
                
                {selectedOptionData.consequences && (
                  <div className="border-t pt-4">
                    <h5 className="font-medium text-[var(--charcoal)] mb-2">Consequences:</h5>
                    <p className="text-[var(--grey)] text-sm leading-relaxed">{selectedOptionData.consequences}</p>
                  </div>
                )}
              </div>

              {/* Next Button */}
              <div className="text-center">
                <Button
                  onClick={handleNext}
                  className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white px-8 py-3"
                >
                  {currentStepIndex === totalSteps - 1 && currentScenarioIndex === totalScenarios - 1 
                    ? 'Complete' 
                    : currentStepIndex === totalSteps - 1 
                    ? 'Next Scenario' 
                    : 'Next Step'}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </motion.div>
          )}
        </motion.div>
      </AnimatePresence>

      {/* Learning Outcomes */}
      {currentScenario?.learningOutcomes && (
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-[var(--color-border)]">
          <h4 className="font-semibold text-[var(--charcoal)] mb-4 flex items-center gap-2">
            <Target className="w-5 h-5" />
            Learning Outcomes
          </h4>
          <ul className="space-y-2">
            {currentScenario.learningOutcomes.map((outcome, index) => (
              <li key={index} className="flex items-start gap-2 text-sm text-[var(--grey)]">
                <CheckCircle2 className="w-4 h-4 text-[var(--emerald)] mt-0.5 flex-shrink-0" />
                {outcome}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
