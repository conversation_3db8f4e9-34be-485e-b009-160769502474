"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { RotateCcw, ChevronLeft, ChevronRight, CheckCircle2, X, Shuffle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { type LearningTool } from "@/Services/planService";

interface FlashcardData {
  id: string;
  front: string;
  back: string;
  difficulty?: "easy" | "medium" | "hard";
}

interface FlashcardsProps {
  tool: LearningTool;
  onComplete: (score?: number) => void;
  isCompleted: boolean;
}

export default function Flashcards({ tool, onComplete, isCompleted }: FlashcardsProps) {
  const flashcards: FlashcardData[] = tool.content?.flashcards || [];
  
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [masteredCards, setMasteredCards] = useState<Set<string>>(new Set());
  const [strugglingCards, setStrugglingCards] = useState<Set<string>>(new Set());
  const [isShuffled, setIsShuffled] = useState(false);
  const [shuffledIndices, setShuffledIndices] = useState<number[]>([]);

  const currentCard = flashcards[shuffledIndices[currentIndex] || currentIndex];
  const totalCards = flashcards.length;
  const masteredCount = masteredCards.size;
  const progressPercentage = totalCards > 0 ? Math.round((masteredCount / totalCards) * 100) : 0;

  useEffect(() => {
    if (totalCards > 0) {
      setShuffledIndices(Array.from({ length: totalCards }, (_, i) => i));
    }
  }, [totalCards]);

  const shuffleCards = () => {
    const indices = Array.from({ length: totalCards }, (_, i) => i);
    for (let i = indices.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [indices[i], indices[j]] = [indices[j], indices[i]];
    }
    setShuffledIndices(indices);
    setCurrentIndex(0);
    setIsFlipped(false);
    setIsShuffled(true);
  };

  const handleCardFlip = () => {
    setIsFlipped(!isFlipped);
  };

  const handleMastered = () => {
    if (currentCard) {
      setMasteredCards(prev => new Set([...prev, currentCard.id]));
      setStrugglingCards(prev => {
        const newSet = new Set(prev);
        newSet.delete(currentCard.id);
        return newSet;
      });
      handleNext();
    }
  };

  const handleStruggling = () => {
    if (currentCard) {
      setStrugglingCards(prev => new Set([...prev, currentCard.id]));
      setMasteredCards(prev => {
        const newSet = new Set(prev);
        newSet.delete(currentCard.id);
        return newSet;
      });
      handleNext();
    }
  };

  const handleNext = () => {
    if (currentIndex < totalCards - 1) {
      setCurrentIndex(prev => prev + 1);
      setIsFlipped(false);
    } else {
      // Completed all cards
      if (masteredCount + 1 >= totalCards * 0.8) { // 80% mastery threshold
        const score = Math.round((masteredCount + 1) / totalCards * 100);
        onComplete(score);
      }
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
      setIsFlipped(false);
    }
  };

  const handleReset = () => {
    setCurrentIndex(0);
    setIsFlipped(false);
    setMasteredCards(new Set());
    setStrugglingCards(new Set());
    setIsShuffled(false);
    setShuffledIndices(Array.from({ length: totalCards }, (_, i) => i));
  };

  if (totalCards === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <X className="w-8 h-8 text-red-600" />
        </div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">No Flashcards Available</h3>
        <p className="text-[var(--grey)]">This tool doesn't have any flashcards configured.</p>
      </div>
    );
  }

  if (isCompleted) {
    return (
      <div className="text-center py-12">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="w-16 h-16 bg-[var(--emerald)]/10 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <CheckCircle2 className="w-8 h-8 text-[var(--emerald)]" />
        </motion.div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">Flashcards Completed!</h3>
        <p className="text-[var(--grey)] mb-6">Great job mastering these concepts.</p>
        <Button onClick={handleReset} variant="outline">
          <RotateCcw className="w-4 h-4 mr-2" />
          Review Again
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-3xl font-bold text-[var(--charcoal)] mb-2"
        >
          {tool.name}
        </motion.h2>
        <p className="text-[var(--grey)] text-lg">{tool.description}</p>
      </div>

      {/* Progress */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-[var(--charcoal)]">
            Card {currentIndex + 1} of {totalCards}
          </span>
          <span className="text-sm text-[var(--grey)]">
            {masteredCount} mastered ({progressPercentage}%)
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className="bg-[var(--emerald)] h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-center gap-4 mb-8">
        <Button onClick={shuffleCards} variant="outline" size="sm">
          <Shuffle className="w-4 h-4 mr-2" />
          {isShuffled ? 'Shuffled' : 'Shuffle'}
        </Button>
        <Button onClick={handleReset} variant="outline" size="sm">
          <RotateCcw className="w-4 h-4 mr-2" />
          Reset
        </Button>
      </div>

      {/* Flashcard */}
      <div className="flex items-center justify-center mb-8">
        <AnimatePresence mode="wait">
          <motion.div
            key={`${currentCard?.id}-${isFlipped}`}
            initial={{ rotateY: 90, opacity: 0 }}
            animate={{ rotateY: 0, opacity: 1 }}
            exit={{ rotateY: -90, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="relative"
          >
            <div
              onClick={handleCardFlip}
              className={cn(
                "w-96 h-64 bg-white rounded-2xl shadow-lg border border-[var(--color-border)]",
                "flex items-center justify-center p-8 cursor-pointer",
                "hover:shadow-xl transition-shadow duration-300",
                "transform-gpu perspective-1000"
              )}
            >
              <div className="text-center">
                <div className="text-sm text-[var(--grey)] mb-4">
                  {isFlipped ? 'Answer' : 'Question'} • Click to flip
                </div>
                <div className="text-lg font-medium text-[var(--charcoal)] leading-relaxed">
                  {isFlipped ? currentCard?.back : currentCard?.front}
                </div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Action Buttons */}
      {isFlipped && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-center gap-4 mb-8"
        >
          <Button
            onClick={handleStruggling}
            variant="outline"
            className="border-red-200 text-red-600 hover:bg-red-50"
          >
            <X className="w-4 h-4 mr-2" />
            Need More Practice
          </Button>
          <Button
            onClick={handleMastered}
            className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
          >
            <CheckCircle2 className="w-4 h-4 mr-2" />
            Got It!
          </Button>
        </motion.div>
      )}

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          onClick={handlePrevious}
          disabled={currentIndex === 0}
          variant="outline"
          className={cn(
            "flex items-center gap-2",
            currentIndex === 0 && "opacity-50 cursor-not-allowed"
          )}
        >
          <ChevronLeft className="w-4 h-4" />
          Previous
        </Button>

        <div className="flex items-center gap-2">
          {Array.from({ length: Math.min(totalCards, 5) }, (_, i) => {
            const cardIndex = currentIndex - 2 + i;
            if (cardIndex < 0 || cardIndex >= totalCards) return null;
            
            const cardId = flashcards[shuffledIndices[cardIndex] || cardIndex]?.id;
            return (
              <div
                key={cardIndex}
                className={cn(
                  "w-2 h-2 rounded-full transition-colors",
                  cardIndex === currentIndex
                    ? "bg-[var(--emerald)]"
                    : masteredCards.has(cardId)
                    ? "bg-[var(--bright-green)]"
                    : strugglingCards.has(cardId)
                    ? "bg-red-400"
                    : "bg-gray-300"
                )}
              />
            );
          })}
        </div>

        <Button
          onClick={handleNext}
          disabled={currentIndex === totalCards - 1}
          className={cn(
            "flex items-center gap-2 bg-[var(--emerald)] hover:bg-[var(--emerald-deep)]",
            currentIndex === totalCards - 1 && "opacity-50 cursor-not-allowed"
          )}
        >
          Next
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>

      {/* Stats */}
      <div className="mt-8 grid grid-cols-3 gap-4">
        <div className="text-center p-4 bg-white rounded-lg border border-[var(--color-border)]">
          <div className="text-2xl font-bold text-[var(--emerald)]">{masteredCards.size}</div>
          <div className="text-sm text-[var(--grey)]">Mastered</div>
        </div>
        <div className="text-center p-4 bg-white rounded-lg border border-[var(--color-border)]">
          <div className="text-2xl font-bold text-red-500">{strugglingCards.size}</div>
          <div className="text-sm text-[var(--grey)]">Need Practice</div>
        </div>
        <div className="text-center p-4 bg-white rounded-lg border border-[var(--color-border)]">
          <div className="text-2xl font-bold text-[var(--grey)]">{totalCards - masteredCards.size - strugglingCards.size}</div>
          <div className="text-sm text-[var(--grey)]">Not Reviewed</div>
        </div>
      </div>
    </div>
  );
}
