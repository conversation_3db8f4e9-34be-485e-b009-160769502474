"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronLeft, ChevronRight, RotateCcw, CheckCircle, Eye, EyeOff, Shuffle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface FlashcardData {
  id: string;
  front: string;
  back: string;
  difficulty?: "easy" | "medium" | "hard";
  category?: string;
}

interface FlashcardsProps {
  content: {
    flashcards: FlashcardData[];
    title?: string;
    description?: string;
  };
  onProgressUpdate: (progress: number, completed?: boolean) => void;
  progress: number;
  isCompleted: boolean;
}

export default function Flashcards({ content, onProgressUpdate, progress, isCompleted }: FlashcardsProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [viewedCards, setViewedCards] = useState<Set<number>>(new Set());
  const [shuffledCards, setShuffledCards] = useState<FlashcardData[]>([]);
  const [isShuffled, setIsShuffled] = useState(false);
  const [showAll, setShowAll] = useState(false);

  const flashcards = content?.flashcards || [];

  useEffect(() => {
    setShuffledCards([...flashcards]);
  }, [flashcards]);

  useEffect(() => {
    if (viewedCards.size > 0) {
      const newProgress = Math.round((viewedCards.size / flashcards.length) * 100);
      const completed = viewedCards.size === flashcards.length;
      onProgressUpdate(newProgress, completed);
    }
  }, [viewedCards, flashcards.length, onProgressUpdate]);

  const handleNext = () => {
    if (currentIndex < shuffledCards.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setIsFlipped(false);
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setIsFlipped(false);
    }
  };

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
    if (!isFlipped) {
      setViewedCards(prev => new Set([...prev, currentIndex]));
    }
  };

  const handleShuffle = () => {
    const shuffled = [...flashcards].sort(() => Math.random() - 0.5);
    setShuffledCards(shuffled);
    setCurrentIndex(0);
    setIsFlipped(false);
    setIsShuffled(true);
  };

  const handleReset = () => {
    setCurrentIndex(0);
    setIsFlipped(false);
    setViewedCards(new Set());
    setShuffledCards([...flashcards]);
    setIsShuffled(false);
    onProgressUpdate(0, false);
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'hard':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (flashcards.length === 0) {
    return (
      <div className="p-8 text-center">
        <p className="text-[var(--grey)]">No flashcards available for this lesson.</p>
      </div>
    );
  }

  const currentCard = shuffledCards[currentIndex];

  return (
    <div className="p-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--charcoal)] mb-2">
          {content.title || "Flashcards"}
        </h2>
        {content.description && (
          <p className="text-[var(--grey)] max-w-2xl mx-auto">
            {content.description}
          </p>
        )}
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={handleShuffle}
            className="text-[var(--emerald)] border-[var(--emerald)]"
          >
            <Shuffle className="w-4 h-4 mr-2" />
            Shuffle
          </Button>
          <Button
            variant="outline"
            onClick={handleReset}
            className="text-[var(--grey)] border-[var(--color-border)]"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowAll(!showAll)}
            className="text-[var(--charcoal)] border-[var(--color-border)]"
          >
            {showAll ? <EyeOff className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
            {showAll ? 'Hide All' : 'Show All'}
          </Button>
        </div>

        <div className="flex items-center gap-4 text-sm text-[var(--grey)]">
          <span>{currentIndex + 1} of {shuffledCards.length}</span>
          <span>•</span>
          <span>{viewedCards.size} viewed</span>
          {isCompleted && (
            <>
              <span>•</span>
              <div className="flex items-center gap-1 text-[var(--emerald)]">
                <CheckCircle className="w-4 h-4" />
                Complete
              </div>
            </>
          )}
        </div>
      </div>

      {showAll ? (
        /* Show All Cards View */
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {shuffledCards.map((card, index) => (
            <motion.div
              key={card.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-xl border border-[var(--color-border)] p-6 shadow-sm"
            >
              {card.difficulty && (
                <div className={cn(
                  "inline-block px-2 py-1 rounded-full text-xs font-medium mb-3 border",
                  getDifficultyColor(card.difficulty)
                )}>
                  {card.difficulty}
                </div>
              )}
              <div className="mb-4">
                <h4 className="font-semibold text-[var(--charcoal)] mb-2">Front:</h4>
                <p className="text-sm text-[var(--grey)]">{card.front}</p>
              </div>
              <div>
                <h4 className="font-semibold text-[var(--charcoal)] mb-2">Back:</h4>
                <p className="text-sm text-[var(--grey)]">{card.back}</p>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        /* Single Card View */
        <div className="max-w-2xl mx-auto">
          {/* Progress Indicator */}
          <div className="flex justify-center mb-8">
            <div className="flex gap-2">
              {shuffledCards.map((_, index) => (
                <div
                  key={index}
                  className={cn(
                    "w-3 h-3 rounded-full transition-all duration-200",
                    index === currentIndex
                      ? "bg-[var(--emerald)] scale-125"
                      : viewedCards.has(index)
                      ? "bg-[var(--bright-green)]"
                      : "bg-gray-200"
                  )}
                />
              ))}
            </div>
          </div>

          {/* Flashcard */}
          <div className="relative h-96 mb-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={`${currentIndex}-${isFlipped}`}
                initial={{ rotateY: 90, opacity: 0 }}
                animate={{ rotateY: 0, opacity: 1 }}
                exit={{ rotateY: -90, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className={cn(
                  "absolute inset-0 rounded-2xl shadow-lg cursor-pointer",
                  "flex items-center justify-center p-8 text-center",
                  isFlipped 
                    ? "bg-gradient-to-br from-[var(--emerald)] to-[var(--emerald-deep)] text-white"
                    : "bg-white border-2 border-[var(--color-border)] text-[var(--charcoal)]"
                )}
                onClick={handleFlip}
                style={{ transformStyle: "preserve-3d" }}
              >
                <div className="w-full">
                  {currentCard.difficulty && !isFlipped && (
                    <div className={cn(
                      "inline-block px-3 py-1 rounded-full text-xs font-medium mb-4 border",
                      getDifficultyColor(currentCard.difficulty)
                    )}>
                      {currentCard.difficulty}
                    </div>
                  )}
                  
                  <div className={cn(
                    "text-xl font-semibold mb-4",
                    isFlipped ? "text-white/90" : "text-[var(--charcoal)]"
                  )}>
                    {isFlipped ? "Answer" : "Question"}
                  </div>
                  
                  <div className={cn(
                    "text-lg leading-relaxed",
                    isFlipped ? "text-white" : "text-[var(--charcoal)]"
                  )}>
                    {isFlipped ? currentCard.back : currentCard.front}
                  </div>
                  
                  {!isFlipped && (
                    <div className="mt-6 text-sm text-[var(--grey)]">
                      Click to reveal answer
                    </div>
                  )}
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentIndex === 0}
              className="flex items-center gap-2"
            >
              <ChevronLeft className="w-4 h-4" />
              Previous
            </Button>

            <Button
              onClick={handleFlip}
              className={cn(
                "px-8 py-3",
                isFlipped 
                  ? "bg-[var(--bright-green)] hover:bg-[var(--lime-green)] text-white"
                  : "bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
              )}
            >
              {isFlipped ? "Hide Answer" : "Show Answer"}
            </Button>

            <Button
              variant="outline"
              onClick={handleNext}
              disabled={currentIndex === shuffledCards.length - 1}
              className="flex items-center gap-2"
            >
              Next
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Completion Message */}
      {isCompleted && !showAll && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mt-8 p-6 bg-gradient-to-r from-[var(--emerald)]/10 to-[var(--bright-green)]/10 rounded-2xl border border-[var(--emerald)]/20"
        >
          <CheckCircle className="w-12 h-12 text-[var(--emerald)] mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-[var(--charcoal)] mb-2">
            Congratulations!
          </h3>
          <p className="text-[var(--grey)]">
            You've viewed all {flashcards.length} flashcards. Great job!
          </p>
        </motion.div>
      )}
    </div>
  );
}
