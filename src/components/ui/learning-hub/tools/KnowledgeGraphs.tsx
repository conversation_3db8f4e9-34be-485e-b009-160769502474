"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ZoomIn, ZoomOut, RotateCcw, Info, Eye, EyeOff, Maximize2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface GraphNode {
  id: string;
  label: string;
  description: string;
  category?: string;
  x: number;
  y: number;
  color?: string;
}

interface GraphEdge {
  id: string;
  source: string;
  target: string;
  label: string;
  description: string;
  type?: "solid" | "dashed" | "dotted";
}

interface KnowledgeGraphsProps {
  content: {
    nodes: GraphNode[];
    edges: GraphEdge[];
    title?: string;
    description?: string;
  };
  onProgressUpdate: (progress: number, completed?: boolean) => void;
  progress: number;
  isCompleted: boolean;
}

export default function KnowledgeGraphs({ content, onProgressUpdate, progress, isCompleted }: KnowledgeGraphsProps) {
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<GraphEdge | null>(null);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [viewedNodes, setViewedNodes] = useState<Set<string>>(new Set());
  const [showLabels, setShowLabels] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const svgRef = useRef<SVGSVGElement>(null);

  const nodes = content?.nodes || [];
  const edges = content?.edges || [];

  useEffect(() => {
    if (viewedNodes.size > 0) {
      const newProgress = Math.round((viewedNodes.size / nodes.length) * 100);
      const completed = viewedNodes.size === nodes.length;
      onProgressUpdate(newProgress, completed);
    }
  }, [viewedNodes, nodes.length, onProgressUpdate]);

  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node);
    setSelectedEdge(null);
    setViewedNodes(prev => new Set([...prev, node.id]));
  };

  const handleEdgeClick = (edge: GraphEdge) => {
    setSelectedEdge(edge);
    setSelectedNode(null);
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.2, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.2, 0.3));
  };

  const handleReset = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
    setSelectedNode(null);
    setSelectedEdge(null);
    setViewedNodes(new Set());
    onProgressUpdate(0, false);
  };

  const getNodeColor = (node: GraphNode) => {
    if (node.color) return node.color;
    
    switch (node.category) {
      case 'concept':
        return '#026c4a'; // emerald
      case 'process':
        return '#29b06f'; // bright-green
      case 'tool':
        return '#88c766'; // lime-green
      case 'outcome':
        return '#6a6c73'; // grey
      default:
        return '#026c4a';
    }
  };

  const getEdgeStyle = (edge: GraphEdge) => {
    switch (edge.type) {
      case 'dashed':
        return '5,5';
      case 'dotted':
        return '2,3';
      default:
        return 'none';
    }
  };

  if (nodes.length === 0) {
    return (
      <div className="p-8 text-center">
        <p className="text-[var(--grey)]">No knowledge graph available for this lesson.</p>
      </div>
    );
  }

  return (
    <div className={cn("p-8", isFullscreen && "fixed inset-0 z-50 bg-white")}>
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--charcoal)] mb-2">
          {content.title || "Knowledge Graph"}
        </h2>
        {content.description && (
          <p className="text-[var(--grey)] max-w-2xl mx-auto">
            {content.description}
          </p>
        )}
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={handleZoomIn}
            className="text-[var(--emerald)] border-[var(--emerald)]"
          >
            <ZoomIn className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            onClick={handleZoomOut}
            className="text-[var(--emerald)] border-[var(--emerald)]"
          >
            <ZoomOut className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            onClick={handleReset}
            className="text-[var(--grey)] border-[var(--color-border)]"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowLabels(!showLabels)}
            className="text-[var(--charcoal)] border-[var(--color-border)]"
          >
            {showLabels ? <EyeOff className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
            {showLabels ? 'Hide Labels' : 'Show Labels'}
          </Button>
          <Button
            variant="outline"
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="text-[var(--charcoal)] border-[var(--color-border)]"
          >
            <Maximize2 className="w-4 h-4 mr-2" />
            {isFullscreen ? 'Exit' : 'Fullscreen'}
          </Button>
        </div>

        <div className="flex items-center gap-4 text-sm text-[var(--grey)]">
          <span>Zoom: {Math.round(zoom * 100)}%</span>
          <span>•</span>
          <span>{viewedNodes.size} of {nodes.length} explored</span>
        </div>
      </div>

      <div className="flex gap-6">
        {/* Graph Visualization */}
        <div className="flex-1">
          <div className="bg-white rounded-2xl border border-[var(--color-border)] overflow-hidden">
            <svg
              ref={svgRef}
              width="100%"
              height="600"
              viewBox={`${-400 + pan.x} ${-300 + pan.y} ${800 / zoom} ${600 / zoom}`}
              className="cursor-move"
            >
              {/* Edges */}
              <g>
                {edges.map((edge) => {
                  const sourceNode = nodes.find(n => n.id === edge.source);
                  const targetNode = nodes.find(n => n.id === edge.target);
                  
                  if (!sourceNode || !targetNode) return null;
                  
                  return (
                    <g key={edge.id}>
                      <line
                        x1={sourceNode.x}
                        y1={sourceNode.y}
                        x2={targetNode.x}
                        y2={targetNode.y}
                        stroke={selectedEdge?.id === edge.id ? "#026c4a" : "#e5e7eb"}
                        strokeWidth={selectedEdge?.id === edge.id ? 3 : 2}
                        strokeDasharray={getEdgeStyle(edge)}
                        className="cursor-pointer"
                        onClick={() => handleEdgeClick(edge)}
                      />
                      {showLabels && (
                        <text
                          x={(sourceNode.x + targetNode.x) / 2}
                          y={(sourceNode.y + targetNode.y) / 2}
                          textAnchor="middle"
                          className="text-xs fill-[var(--grey)] pointer-events-none"
                          dy="-5"
                        >
                          {edge.label}
                        </text>
                      )}
                    </g>
                  );
                })}
              </g>

              {/* Nodes */}
              <g>
                {nodes.map((node) => {
                  const isViewed = viewedNodes.has(node.id);
                  const isSelected = selectedNode?.id === node.id;
                  
                  return (
                    <g key={node.id}>
                      <circle
                        cx={node.x}
                        cy={node.y}
                        r={isSelected ? 25 : 20}
                        fill={getNodeColor(node)}
                        stroke={isSelected ? "#ffffff" : isViewed ? "#29b06f" : "#e5e7eb"}
                        strokeWidth={isSelected ? 4 : 2}
                        className="cursor-pointer transition-all duration-200"
                        onClick={() => handleNodeClick(node)}
                        opacity={isViewed ? 1 : 0.7}
                      />
                      {showLabels && (
                        <text
                          x={node.x}
                          y={node.y + 35}
                          textAnchor="middle"
                          className="text-xs fill-[var(--charcoal)] pointer-events-none font-medium"
                        >
                          {node.label}
                        </text>
                      )}
                      {isViewed && (
                        <circle
                          cx={node.x + 15}
                          cy={node.y - 15}
                          r={8}
                          fill="#29b06f"
                          className="pointer-events-none"
                        />
                      )}
                    </g>
                  );
                })}
              </g>
            </svg>
          </div>
        </div>

        {/* Info Panel */}
        <div className="w-80">
          <div className="bg-white rounded-2xl p-6 border border-[var(--color-border)] sticky top-0">
            {selectedNode ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                key={selectedNode.id}
              >
                <div className="flex items-center gap-3 mb-4">
                  <div
                    className="w-6 h-6 rounded-full"
                    style={{ backgroundColor: getNodeColor(selectedNode) }}
                  />
                  <h3 className="text-lg font-semibold text-[var(--charcoal)]">
                    {selectedNode.label}
                  </h3>
                </div>
                
                {selectedNode.category && (
                  <div className="inline-block px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200 mb-4">
                    {selectedNode.category}
                  </div>
                )}
                
                <p className="text-[var(--grey)] leading-relaxed">
                  {selectedNode.description}
                </p>

                {/* Connected Nodes */}
                <div className="mt-6">
                  <h4 className="font-medium text-[var(--charcoal)] mb-3">Connected to:</h4>
                  <div className="space-y-2">
                    {edges
                      .filter(edge => edge.source === selectedNode.id || edge.target === selectedNode.id)
                      .map(edge => {
                        const connectedNodeId = edge.source === selectedNode.id ? edge.target : edge.source;
                        const connectedNode = nodes.find(n => n.id === connectedNodeId);
                        
                        if (!connectedNode) return null;
                        
                        return (
                          <div
                            key={edge.id}
                            className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-50 cursor-pointer"
                            onClick={() => handleNodeClick(connectedNode)}
                          >
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: getNodeColor(connectedNode) }}
                            />
                            <span className="text-sm text-[var(--charcoal)]">
                              {connectedNode.label}
                            </span>
                          </div>
                        );
                      })}
                  </div>
                </div>
              </motion.div>
            ) : selectedEdge ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                key={selectedEdge.id}
              >
                <div className="flex items-center gap-3 mb-4">
                  <Info className="w-6 h-6 text-[var(--emerald)]" />
                  <h3 className="text-lg font-semibold text-[var(--charcoal)]">
                    {selectedEdge.label}
                  </h3>
                </div>
                
                <p className="text-[var(--grey)] leading-relaxed">
                  {selectedEdge.description}
                </p>

                {/* Connected Nodes */}
                <div className="mt-6">
                  <h4 className="font-medium text-[var(--charcoal)] mb-3">Connects:</h4>
                  <div className="space-y-2">
                    {[selectedEdge.source, selectedEdge.target].map(nodeId => {
                      const node = nodes.find(n => n.id === nodeId);
                      if (!node) return null;
                      
                      return (
                        <div
                          key={nodeId}
                          className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-50 cursor-pointer"
                          onClick={() => handleNodeClick(node)}
                        >
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: getNodeColor(node) }}
                          />
                          <span className="text-sm text-[var(--charcoal)]">
                            {node.label}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </motion.div>
            ) : (
              <div className="text-center py-8">
                <Info className="w-12 h-12 text-[var(--grey)] mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">
                  Explore the Graph
                </h3>
                <p className="text-[var(--grey)] text-sm">
                  Click on nodes and edges to learn more about the relationships between concepts.
                </p>
              </div>
            )}
          </div>

          {/* Legend */}
          <div className="bg-white rounded-2xl p-6 border border-[var(--color-border)] mt-4">
            <h4 className="font-medium text-[var(--charcoal)] mb-3">Legend</h4>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-[var(--emerald)]" />
                <span className="text-xs text-[var(--grey)]">Concept</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-[var(--bright-green)]" />
                <span className="text-xs text-[var(--grey)]">Process</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-[var(--lime-green)]" />
                <span className="text-xs text-[var(--grey)]">Tool</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-[var(--grey)]" />
                <span className="text-xs text-[var(--grey)]">Outcome</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress */}
      {isCompleted && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mt-8 p-6 bg-gradient-to-r from-[var(--emerald)]/10 to-[var(--bright-green)]/10 rounded-2xl border border-[var(--emerald)]/20"
        >
          <div className="text-4xl mb-4">🕸️</div>
          <h3 className="text-xl font-semibold text-[var(--charcoal)] mb-2">
            Graph Fully Explored!
          </h3>
          <p className="text-[var(--grey)]">
            You've explored all {nodes.length} concepts in this knowledge graph.
          </p>
        </motion.div>
      )}
    </div>
  );
}
