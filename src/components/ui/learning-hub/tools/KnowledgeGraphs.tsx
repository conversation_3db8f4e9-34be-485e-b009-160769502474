"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CheckCircle2, X, ZoomIn, ZoomOut, RotateCcw, Network, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { type LearningTool } from "@/Services/planService";

interface GraphNode {
  id: string;
  label: string;
  type: "concept" | "subconcept" | "detail";
  description?: string;
  x: number;
  y: number;
  color?: string;
}

interface GraphEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  type: "relates_to" | "part_of" | "leads_to" | "depends_on";
}

interface KnowledgeGraphData {
  title: string;
  description: string;
  nodes: GraphNode[];
  edges: GraphEdge[];
  learningObjectives: string[];
}

interface KnowledgeGraphsProps {
  tool: LearningTool;
  onComplete: (score?: number) => void;
  isCompleted: boolean;
}

export default function KnowledgeGraphs({ tool, onComplete, isCompleted }: KnowledgeGraphsProps) {
  const graphData: KnowledgeGraphData = tool.content?.graph || { title: "", description: "", nodes: [], edges: [], learningObjectives: [] };
  
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [exploredNodes, setExploredNodes] = useState<Set<string>>(new Set());
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const svgRef = useRef<SVGSVGElement>(null);

  const totalNodes = graphData.nodes.length;
  const exploredCount = exploredNodes.size;
  const progressPercentage = totalNodes > 0 ? Math.round((exploredCount / totalNodes) * 100) : 0;

  useEffect(() => {
    // Auto-complete when 80% of nodes are explored
    if (exploredCount >= totalNodes * 0.8 && totalNodes > 0) {
      const score = Math.round((exploredCount / totalNodes) * 100);
      onComplete(score);
    }
  }, [exploredCount, totalNodes, onComplete]);

  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node);
    setExploredNodes(prev => new Set([...prev, node.id]));
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.2, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.2, 0.5));
  };

  const handleReset = () => {
    setSelectedNode(null);
    setExploredNodes(new Set());
    setZoom(1);
    setPan({ x: 0, y: 0 });
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPan({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const getNodeColor = (node: GraphNode) => {
    if (node.color) return node.color;
    
    switch (node.type) {
      case "concept":
        return "var(--emerald)";
      case "subconcept":
        return "var(--bright-green)";
      case "detail":
        return "var(--lime-green)";
      default:
        return "var(--grey)";
    }
  };

  const getEdgeColor = (edge: GraphEdge) => {
    switch (edge.type) {
      case "relates_to":
        return "#94a3b8";
      case "part_of":
        return "var(--emerald)";
      case "leads_to":
        return "var(--bright-green)";
      case "depends_on":
        return "#f59e0b";
      default:
        return "#94a3b8";
    }
  };

  if (totalNodes === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <X className="w-8 h-8 text-red-600" />
        </div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">No Knowledge Graph Available</h3>
        <p className="text-[var(--grey)]">This tool doesn't have a knowledge graph configured.</p>
      </div>
    );
  }

  if (isCompleted) {
    return (
      <div className="text-center py-12">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="w-16 h-16 bg-[var(--emerald)]/10 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <CheckCircle2 className="w-8 h-8 text-[var(--emerald)]" />
        </motion.div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">Knowledge Graph Explored!</h3>
        <p className="text-[var(--grey)] mb-6">
          You've explored {exploredCount} out of {totalNodes} concepts ({progressPercentage}% coverage).
        </p>
        
        <div className="grid grid-cols-2 gap-4 max-w-md mx-auto mb-6">
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--emerald)]">{exploredCount}</div>
            <div className="text-sm text-[var(--grey)]">Concepts Explored</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--bright-green)]">{progressPercentage}%</div>
            <div className="text-sm text-[var(--grey)]">Coverage</div>
          </div>
        </div>
        
        <Button onClick={handleReset} variant="outline">
          <RotateCcw className="w-4 h-4 mr-2" />
          Explore Again
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-3xl font-bold text-[var(--charcoal)] mb-2"
        >
          {tool.name}
        </motion.h2>
        <p className="text-[var(--grey)] text-lg">{tool.description}</p>
        <div className="flex items-center justify-center gap-2 mt-4 text-sm text-[var(--grey)]">
          <Network className="w-4 h-4" />
          Click on nodes to explore concepts and their relationships
        </div>
      </div>

      {/* Progress */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-[var(--charcoal)]">
            Exploration Progress
          </span>
          <span className="text-sm text-[var(--grey)]">
            {exploredCount}/{totalNodes} concepts explored ({progressPercentage}%)
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className="bg-[var(--emerald)] h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Graph Visualization */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-[var(--color-border)]">
            {/* Controls */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-[var(--charcoal)]">{graphData.title}</h3>
              <div className="flex items-center gap-2">
                <Button onClick={handleZoomOut} size="sm" variant="outline">
                  <ZoomOut className="w-4 h-4" />
                </Button>
                <Button onClick={handleZoomIn} size="sm" variant="outline">
                  <ZoomIn className="w-4 h-4" />
                </Button>
                <Button onClick={handleReset} size="sm" variant="outline">
                  <RotateCcw className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* SVG Graph */}
            <div className="relative overflow-hidden rounded-xl bg-gray-50 h-96">
              <svg
                ref={svgRef}
                className="w-full h-full cursor-move"
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}
              >
                <g transform={`translate(${pan.x}, ${pan.y}) scale(${zoom})`}>
                  {/* Edges */}
                  {graphData.edges.map((edge) => {
                    const sourceNode = graphData.nodes.find(n => n.id === edge.source);
                    const targetNode = graphData.nodes.find(n => n.id === edge.target);
                    
                    if (!sourceNode || !targetNode) return null;
                    
                    return (
                      <g key={edge.id}>
                        <line
                          x1={sourceNode.x}
                          y1={sourceNode.y}
                          x2={targetNode.x}
                          y2={targetNode.y}
                          stroke={getEdgeColor(edge)}
                          strokeWidth="2"
                          opacity="0.6"
                        />
                        {edge.label && (
                          <text
                            x={(sourceNode.x + targetNode.x) / 2}
                            y={(sourceNode.y + targetNode.y) / 2}
                            textAnchor="middle"
                            className="text-xs fill-[var(--grey)]"
                          >
                            {edge.label}
                          </text>
                        )}
                      </g>
                    );
                  })}

                  {/* Nodes */}
                  {graphData.nodes.map((node) => {
                    const isExplored = exploredNodes.has(node.id);
                    const isSelected = selectedNode?.id === node.id;
                    
                    return (
                      <g key={node.id}>
                        <circle
                          cx={node.x}
                          cy={node.y}
                          r={node.type === "concept" ? 25 : node.type === "subconcept" ? 20 : 15}
                          fill={isExplored ? getNodeColor(node) : "#e5e7eb"}
                          stroke={isSelected ? "#3b82f6" : "white"}
                          strokeWidth={isSelected ? 3 : 2}
                          className="cursor-pointer hover:opacity-80 transition-opacity"
                          onClick={() => handleNodeClick(node)}
                        />
                        <text
                          x={node.x}
                          y={node.y}
                          textAnchor="middle"
                          dominantBaseline="middle"
                          className={cn(
                            "text-xs font-medium pointer-events-none",
                            isExplored ? "fill-white" : "fill-[var(--grey)]"
                          )}
                        >
                          {node.label.length > 10 ? `${node.label.substring(0, 10)}...` : node.label}
                        </text>
                      </g>
                    );
                  })}
                </g>
              </svg>
            </div>

            <p className="text-sm text-[var(--grey)] mt-4">{graphData.description}</p>
          </div>
        </div>

        {/* Node Details */}
        <div className="space-y-6">
          {/* Selected Node Info */}
          <AnimatePresence>
            {selectedNode && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="bg-white rounded-2xl p-6 shadow-lg border border-[var(--color-border)]"
              >
                <div className="flex items-start gap-3 mb-4">
                  <div
                    className="w-4 h-4 rounded-full mt-1"
                    style={{ backgroundColor: getNodeColor(selectedNode) }}
                  />
                  <div>
                    <h3 className="font-semibold text-[var(--charcoal)]">{selectedNode.label}</h3>
                    <p className="text-sm text-[var(--grey)] capitalize">{selectedNode.type}</p>
                  </div>
                </div>
                {selectedNode.description && (
                  <p className="text-sm text-[var(--grey)] leading-relaxed">
                    {selectedNode.description}
                  </p>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Learning Objectives */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-[var(--color-border)]">
            <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-4 flex items-center gap-2">
              <Info className="w-5 h-5" />
              Learning Objectives
            </h3>
            <ul className="space-y-2">
              {graphData.learningObjectives.map((objective, index) => (
                <li key={index} className="flex items-start gap-2 text-sm text-[var(--grey)]">
                  <CheckCircle2 className="w-4 h-4 text-[var(--emerald)] mt-0.5 flex-shrink-0" />
                  {objective}
                </li>
              ))}
            </ul>
          </div>

          {/* Legend */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-[var(--color-border)]">
            <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-4">Legend</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 rounded-full" style={{ backgroundColor: "var(--emerald)" }} />
                <span className="text-sm text-[var(--grey)]">Main Concepts</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-5 h-5 rounded-full" style={{ backgroundColor: "var(--bright-green)" }} />
                <span className="text-sm text-[var(--grey)]">Sub-concepts</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-4 h-4 rounded-full" style={{ backgroundColor: "var(--lime-green)" }} />
                <span className="text-sm text-[var(--grey)]">Details</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-4 h-4 rounded-full bg-gray-300" />
                <span className="text-sm text-[var(--grey)]">Unexplored</span>
              </div>
            </div>
          </div>

          {/* Progress Stats */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-[var(--color-border)]">
            <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-4">Progress</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-[var(--grey)]">Concepts Explored</span>
                <span className="text-sm font-medium text-[var(--charcoal)]">{exploredCount}/{totalNodes}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-[var(--grey)]">Coverage</span>
                <span className="text-sm font-medium text-[var(--charcoal)]">{progressPercentage}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
