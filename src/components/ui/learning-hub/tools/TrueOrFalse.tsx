"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Check, X, ChevronRight, RotateCcw, CheckCircle, AlertCircle, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface TrueOrFalseQuestion {
  id: string;
  statement: string;
  correct: boolean;
  explanation: string;
  difficulty?: "easy" | "medium" | "hard";
  category?: string;
}

interface TrueOrFalseProps {
  content: {
    questions: TrueOrFalseQuestion[];
    title?: string;
    description?: string;
  };
  onProgressUpdate: (progress: number, completed?: boolean) => void;
  progress: number;
  isCompleted: boolean;
}

export default function TrueOrFalse({ content, onProgressUpdate, progress, isCompleted }: TrueOrFalseProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [answers, setAnswers] = useState<Map<number, boolean>>(new Map());
  const [showExplanation, setShowExplanation] = useState(false);
  const [score, setScore] = useState(0);
  const [showResults, setShowResults] = useState(false);

  const questions = content?.questions || [];

  useEffect(() => {
    if (answers.size > 0) {
      const newProgress = Math.round((answers.size / questions.length) * 100);
      const completed = answers.size === questions.length;
      
      if (completed) {
        const correctAnswers = Array.from(answers.entries()).filter(
          ([index, answer]) => answer === questions[index].correct
        ).length;
        setScore(Math.round((correctAnswers / questions.length) * 100));
        setShowResults(true);
      }
      
      onProgressUpdate(newProgress, completed);
    }
  }, [answers, questions.length, onProgressUpdate]);

  const handleAnswer = (answer: boolean) => {
    setAnswers(prev => new Map(prev.set(currentIndex, answer)));
    setShowExplanation(true);
  };

  const handleNext = () => {
    if (currentIndex < questions.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setShowExplanation(false);
    }
  };

  const handleReset = () => {
    setCurrentIndex(0);
    setAnswers(new Map());
    setShowExplanation(false);
    setScore(0);
    setShowResults(false);
    onProgressUpdate(0, false);
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'hard':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (questions.length === 0) {
    return (
      <div className="p-8 text-center">
        <p className="text-[var(--grey)]">No True/False questions available for this lesson.</p>
      </div>
    );
  }

  const currentQuestion = questions[currentIndex];
  const userAnswer = answers.get(currentIndex);
  const isCorrect = userAnswer === currentQuestion.correct;

  if (showResults) {
    return (
      <div className="p-8">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-2xl p-8 shadow-lg border border-[var(--color-border)]"
          >
            <div className="text-6xl mb-6">
              {score >= 80 ? '🎉' : score >= 60 ? '👍' : '📚'}
            </div>
            
            <h2 className="text-3xl font-bold text-[var(--charcoal)] mb-4">
              Quiz Complete!
            </h2>
            
            <div className={cn("text-5xl font-bold mb-6", getScoreColor(score))}>
              {score}%
            </div>
            
            <p className="text-[var(--grey)] text-lg mb-8">
              You got {Array.from(answers.entries()).filter(([index, answer]) => answer === questions[index].correct).length} out of {questions.length} questions correct.
            </p>

            {/* Score Breakdown */}
            <div className="grid md:grid-cols-3 gap-4 mb-8">
              <div className="bg-green-50 rounded-xl p-4 border border-green-200">
                <div className="text-2xl font-bold text-green-600">
                  {Array.from(answers.entries()).filter(([index, answer]) => answer === questions[index].correct).length}
                </div>
                <div className="text-green-700 text-sm">Correct</div>
              </div>
              <div className="bg-red-50 rounded-xl p-4 border border-red-200">
                <div className="text-2xl font-bold text-red-600">
                  {Array.from(answers.entries()).filter(([index, answer]) => answer !== questions[index].correct).length}
                </div>
                <div className="text-red-700 text-sm">Incorrect</div>
              </div>
              <div className="bg-blue-50 rounded-xl p-4 border border-blue-200">
                <div className="text-2xl font-bold text-blue-600">
                  {questions.length}
                </div>
                <div className="text-blue-700 text-sm">Total</div>
              </div>
            </div>

            <div className="flex gap-4 justify-center">
              <Button
                onClick={handleReset}
                className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--charcoal)] mb-2">
          {content.title || "True or False"}
        </h2>
        {content.description && (
          <p className="text-[var(--grey)] max-w-2xl mx-auto">
            {content.description}
          </p>
        )}
      </div>

      {/* Progress */}
      <div className="max-w-4xl mx-auto mb-8">
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm text-[var(--grey)]">
            Question {currentIndex + 1} of {questions.length}
          </span>
          <span className="text-sm text-[var(--grey)]">
            {answers.size} answered
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-[var(--emerald)] h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentIndex + 1) / questions.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Question */}
      <div className="max-w-4xl mx-auto">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-2xl p-8 shadow-lg border border-[var(--color-border)] mb-8"
          >
            {/* Question Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                {currentQuestion.difficulty && (
                  <div className={cn(
                    "px-3 py-1 rounded-full text-xs font-medium border",
                    getDifficultyColor(currentQuestion.difficulty)
                  )}>
                    {currentQuestion.difficulty}
                  </div>
                )}
                {currentQuestion.category && (
                  <div className="px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                    {currentQuestion.category}
                  </div>
                )}
              </div>
              
              {userAnswer !== undefined && (
                <div className={cn(
                  "flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium",
                  isCorrect 
                    ? "bg-green-100 text-green-800 border border-green-200"
                    : "bg-red-100 text-red-800 border border-red-200"
                )}>
                  {isCorrect ? <CheckCircle className="w-4 h-4" /> : <AlertCircle className="w-4 h-4" />}
                  {isCorrect ? 'Correct' : 'Incorrect'}
                </div>
              )}
            </div>

            {/* Statement */}
            <div className="text-xl text-[var(--charcoal)] leading-relaxed mb-8">
              {currentQuestion.statement}
            </div>

            {/* Answer Buttons */}
            {!showExplanation ? (
              <div className="flex gap-4 justify-center">
                <Button
                  onClick={() => handleAnswer(true)}
                  className="flex items-center gap-3 px-8 py-4 text-lg bg-green-600 hover:bg-green-700 text-white"
                  size="lg"
                >
                  <Check className="w-6 h-6" />
                  True
                </Button>
                <Button
                  onClick={() => handleAnswer(false)}
                  className="flex items-center gap-3 px-8 py-4 text-lg bg-red-600 hover:bg-red-700 text-white"
                  size="lg"
                >
                  <X className="w-6 h-6" />
                  False
                </Button>
              </div>
            ) : (
              /* Explanation */
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={cn(
                  "p-6 rounded-xl border-l-4",
                  isCorrect 
                    ? "bg-green-50 border-green-500"
                    : "bg-red-50 border-red-500"
                )}
              >
                <div className="flex items-start gap-3">
                  <Info className={cn(
                    "w-5 h-5 mt-0.5 flex-shrink-0",
                    isCorrect ? "text-green-600" : "text-red-600"
                  )} />
                  <div>
                    <div className={cn(
                      "font-semibold mb-2",
                      isCorrect ? "text-green-800" : "text-red-800"
                    )}>
                      The correct answer is: {currentQuestion.correct ? 'True' : 'False'}
                    </div>
                    <div className={cn(
                      "text-sm leading-relaxed",
                      isCorrect ? "text-green-700" : "text-red-700"
                    )}>
                      {currentQuestion.explanation}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Navigation */}
        {showExplanation && (
          <div className="flex justify-center">
            {currentIndex < questions.length - 1 ? (
              <Button
                onClick={handleNext}
                className="flex items-center gap-2 px-6 py-3 bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
              >
                Next Question
                <ChevronRight className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                onClick={() => setShowResults(true)}
                className="flex items-center gap-2 px-6 py-3 bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
              >
                View Results
                <CheckCircle className="w-4 h-4" />
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
