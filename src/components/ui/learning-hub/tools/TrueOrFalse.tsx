"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CheckCircle2, X, ArrowRight, RotateCcw, Target, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { type LearningTool } from "@/Services/planService";

interface TrueOrFalseQuestion {
  id: string;
  statement: string;
  correct: boolean;
  explanation: string;
  difficulty?: "easy" | "medium" | "hard";
  category?: string;
}

interface TrueOrFalseProps {
  tool: LearningTool;
  onComplete: (score?: number) => void;
  isCompleted: boolean;
}

export default function TrueOrFalse({ tool, onComplete, isCompleted }: TrueOrFalseProps) {
  const questions: TrueOrFalseQuestion[] = tool.content?.questions || [];
  
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<boolean | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);
  const [answers, setAnswers] = useState<{ [key: string]: boolean }>({});
  const [startTime, setStartTime] = useState<Date>(new Date());

  const currentQuestion = questions[currentIndex];
  const totalQuestions = questions.length;
  const answeredCount = Object.keys(answers).length;
  const correctCount = Object.entries(answers).filter(([id, userAnswer]) => {
    const question = questions.find(q => q.id === id);
    return question && question.correct === userAnswer;
  }).length;
  
  const progressPercentage = totalQuestions > 0 ? Math.round((answeredCount / totalQuestions) * 100) : 0;
  const accuracy = answeredCount > 0 ? Math.round((correctCount / answeredCount) * 100) : 0;

  useEffect(() => {
    setStartTime(new Date());
  }, []);

  const handleAnswerSelect = (answer: boolean) => {
    if (showExplanation) return;
    
    setSelectedAnswer(answer);
    setShowExplanation(true);
    
    // Record the answer
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: answer
    }));
  };

  const handleNext = () => {
    if (currentIndex < totalQuestions - 1) {
      setCurrentIndex(prev => prev + 1);
      setSelectedAnswer(null);
      setShowExplanation(false);
    } else {
      // Completed all questions
      const finalScore = Math.round((correctCount / totalQuestions) * 100);
      onComplete(finalScore);
    }
  };

  const handleReset = () => {
    setCurrentIndex(0);
    setSelectedAnswer(null);
    setShowExplanation(false);
    setAnswers({});
    setStartTime(new Date());
  };

  if (totalQuestions === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <X className="w-8 h-8 text-red-600" />
        </div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">No Questions Available</h3>
        <p className="text-[var(--grey)]">This tool doesn't have any True/False questions configured.</p>
      </div>
    );
  }

  if (isCompleted) {
    const timeSpent = Math.round((new Date().getTime() - startTime.getTime()) / 60000);
    
    return (
      <div className="text-center py-12">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="w-16 h-16 bg-[var(--emerald)]/10 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <CheckCircle2 className="w-8 h-8 text-[var(--emerald)]" />
        </motion.div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">True/False Completed!</h3>
        <p className="text-[var(--grey)] mb-6">
          You scored {accuracy}% accuracy on {totalQuestions} questions
          {timeSpent > 0 && ` in ${timeSpent} minutes`}.
        </p>
        
        <div className="grid grid-cols-3 gap-4 max-w-md mx-auto mb-6">
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--emerald)]">{correctCount}</div>
            <div className="text-sm text-[var(--grey)]">Correct</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-red-500">{totalQuestions - correctCount}</div>
            <div className="text-sm text-[var(--grey)]">Incorrect</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--bright-green)]">{accuracy}%</div>
            <div className="text-sm text-[var(--grey)]">Accuracy</div>
          </div>
        </div>
        
        <Button onClick={handleReset} variant="outline">
          <RotateCcw className="w-4 h-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  const isCorrect = selectedAnswer === currentQuestion?.correct;

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-3xl font-bold text-[var(--charcoal)] mb-2"
        >
          {tool.name}
        </motion.h2>
        <p className="text-[var(--grey)] text-lg">{tool.description}</p>
      </div>

      {/* Progress */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-[var(--charcoal)]">
            Question {currentIndex + 1} of {totalQuestions}
          </span>
          <div className="flex items-center gap-4 text-sm text-[var(--grey)]">
            <div className="flex items-center gap-1">
              <Target className="w-4 h-4" />
              {accuracy}% accuracy
            </div>
            <div className="flex items-center gap-1">
              <TrendingUp className="w-4 h-4" />
              {correctCount}/{answeredCount} correct
            </div>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className="bg-[var(--emerald)] h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      {/* Question Card */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentQuestion?.id}
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -50 }}
          transition={{ duration: 0.3 }}
          className="bg-white rounded-2xl p-8 shadow-lg border border-[var(--color-border)] mb-8"
        >
          {/* Question */}
          <div className="text-center mb-8">
            {currentQuestion?.category && (
              <div className="inline-block px-3 py-1 bg-[var(--emerald)]/10 text-[var(--emerald)] rounded-full text-sm font-medium mb-4">
                {currentQuestion.category}
              </div>
            )}
            <h3 className="text-xl font-semibold text-[var(--charcoal)] leading-relaxed">
              {currentQuestion?.statement}
            </h3>
          </div>

          {/* Answer Buttons */}
          {!showExplanation && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center justify-center gap-6"
            >
              <Button
                onClick={() => handleAnswerSelect(true)}
                size="lg"
                className={cn(
                  "px-8 py-4 text-lg font-semibold rounded-xl",
                  "bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white",
                  "transform hover:scale-105 transition-all duration-200"
                )}
              >
                <CheckCircle2 className="w-5 h-5 mr-2" />
                True
              </Button>
              
              <Button
                onClick={() => handleAnswerSelect(false)}
                size="lg"
                variant="outline"
                className={cn(
                  "px-8 py-4 text-lg font-semibold rounded-xl",
                  "border-red-200 text-red-600 hover:bg-red-50",
                  "transform hover:scale-105 transition-all duration-200"
                )}
              >
                <X className="w-5 h-5 mr-2" />
                False
              </Button>
            </motion.div>
          )}

          {/* Explanation */}
          {showExplanation && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              {/* Result */}
              <div className={cn(
                "flex items-center justify-center gap-3 p-4 rounded-xl",
                isCorrect 
                  ? "bg-[var(--emerald)]/10 text-[var(--emerald)]" 
                  : "bg-red-50 text-red-600"
              )}>
                {isCorrect ? (
                  <CheckCircle2 className="w-6 h-6" />
                ) : (
                  <X className="w-6 h-6" />
                )}
                <span className="font-semibold text-lg">
                  {isCorrect ? 'Correct!' : 'Incorrect'}
                </span>
              </div>

              {/* Explanation */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h4 className="font-semibold text-[var(--charcoal)] mb-2">Explanation:</h4>
                <p className="text-[var(--grey)] leading-relaxed">{currentQuestion?.explanation}</p>
              </div>

              {/* Correct Answer */}
              <div className="text-center">
                <p className="text-sm text-[var(--grey)] mb-2">Correct Answer:</p>
                <div className={cn(
                  "inline-flex items-center gap-2 px-4 py-2 rounded-full font-medium",
                  currentQuestion?.correct 
                    ? "bg-[var(--emerald)]/10 text-[var(--emerald)]"
                    : "bg-red-50 text-red-600"
                )}>
                  {currentQuestion?.correct ? (
                    <CheckCircle2 className="w-4 h-4" />
                  ) : (
                    <X className="w-4 h-4" />
                  )}
                  {currentQuestion?.correct ? 'True' : 'False'}
                </div>
              </div>

              {/* Next Button */}
              <div className="text-center">
                <Button
                  onClick={handleNext}
                  className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white px-8 py-3"
                >
                  {currentIndex === totalQuestions - 1 ? 'Complete' : 'Next Question'}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </motion.div>
          )}
        </motion.div>
      </AnimatePresence>

      {/* Question Navigation */}
      <div className="flex items-center justify-center gap-2">
        {questions.map((_, index) => {
          const questionId = questions[index].id;
          const isAnswered = questionId in answers;
          const isCurrentQuestion = index === currentIndex;
          
          return (
            <div
              key={index}
              className={cn(
                "w-3 h-3 rounded-full transition-colors",
                isCurrentQuestion
                  ? "bg-[var(--emerald)] scale-125"
                  : isAnswered
                  ? answers[questionId] === questions[index].correct
                    ? "bg-[var(--bright-green)]"
                    : "bg-red-400"
                  : "bg-gray-300"
              )}
            />
          );
        })}
      </div>
    </div>
  );
}
