"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CheckCircle2, X, RotateCcw, Shuffle, Target, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { type LearningTool } from "@/Services/planService";

interface MatchPair {
  id: string;
  concept: string;
  definition: string;
  category?: string;
}

interface MatchConceptsProps {
  tool: LearningTool;
  onComplete: (score?: number) => void;
  isCompleted: boolean;
}

export default function MatchConcepts({ tool, onComplete, isCompleted }: MatchConceptsProps) {
  const pairs: MatchPair[] = tool.content?.pairs || [];
  
  const [concepts, setConcepts] = useState<string[]>([]);
  const [definitions, setDefinitions] = useState<string[]>([]);
  const [matches, setMatches] = useState<{ [key: string]: string }>({});
  const [selectedConcept, setSelectedConcept] = useState<string | null>(null);
  const [selectedDefinition, setSelectedDefinition] = useState<string | null>(null);
  const [correctMatches, setCorrectMatches] = useState<Set<string>>(new Set());
  const [incorrectMatches, setIncorrectMatches] = useState<Set<string>>(new Set());
  const [showResults, setShowResults] = useState(false);
  const [startTime, setStartTime] = useState<Date>(new Date());

  const totalPairs = pairs.length;
  const matchedCount = Object.keys(matches).length;
  const correctCount = correctMatches.size;
  const progressPercentage = totalPairs > 0 ? Math.round((matchedCount / totalPairs) * 100) : 0;
  const accuracy = matchedCount > 0 ? Math.round((correctCount / matchedCount) * 100) : 0;

  useEffect(() => {
    if (pairs.length > 0) {
      // Shuffle concepts and definitions separately
      const shuffledConcepts = [...pairs.map(p => p.concept)].sort(() => Math.random() - 0.5);
      const shuffledDefinitions = [...pairs.map(p => p.definition)].sort(() => Math.random() - 0.5);
      
      setConcepts(shuffledConcepts);
      setDefinitions(shuffledDefinitions);
    }
    setStartTime(new Date());
  }, [pairs]);

  const handleConceptClick = (concept: string) => {
    if (matches[concept] || showResults) return;
    
    if (selectedConcept === concept) {
      setSelectedConcept(null);
    } else {
      setSelectedConcept(concept);
      if (selectedDefinition) {
        makeMatch(concept, selectedDefinition);
      }
    }
  };

  const handleDefinitionClick = (definition: string) => {
    if (Object.values(matches).includes(definition) || showResults) return;
    
    if (selectedDefinition === definition) {
      setSelectedDefinition(null);
    } else {
      setSelectedDefinition(definition);
      if (selectedConcept) {
        makeMatch(selectedConcept, definition);
      }
    }
  };

  const makeMatch = (concept: string, definition: string) => {
    const pair = pairs.find(p => p.concept === concept && p.definition === definition);
    const isCorrect = !!pair;
    
    setMatches(prev => ({ ...prev, [concept]: definition }));
    
    if (isCorrect) {
      setCorrectMatches(prev => new Set([...prev, concept]));
    } else {
      setIncorrectMatches(prev => new Set([...prev, concept]));
    }
    
    setSelectedConcept(null);
    setSelectedDefinition(null);
    
    // Check if all pairs are matched
    if (matchedCount + 1 === totalPairs) {
      setTimeout(() => {
        setShowResults(true);
        const finalScore = Math.round(((correctMatches.size + (isCorrect ? 1 : 0)) / totalPairs) * 100);
        onComplete(finalScore);
      }, 1000);
    }
  };

  const handleShuffle = () => {
    const shuffledConcepts = [...concepts].sort(() => Math.random() - 0.5);
    const shuffledDefinitions = [...definitions].sort(() => Math.random() - 0.5);
    
    setConcepts(shuffledConcepts);
    setDefinitions(shuffledDefinitions);
  };

  const handleReset = () => {
    setMatches({});
    setCorrectMatches(new Set());
    setIncorrectMatches(new Set());
    setSelectedConcept(null);
    setSelectedDefinition(null);
    setShowResults(false);
    setStartTime(new Date());
    
    // Re-shuffle
    if (pairs.length > 0) {
      const shuffledConcepts = [...pairs.map(p => p.concept)].sort(() => Math.random() - 0.5);
      const shuffledDefinitions = [...pairs.map(p => p.definition)].sort(() => Math.random() - 0.5);
      
      setConcepts(shuffledConcepts);
      setDefinitions(shuffledDefinitions);
    }
  };

  if (totalPairs === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <X className="w-8 h-8 text-red-600" />
        </div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">No Concepts Available</h3>
        <p className="text-[var(--grey)]">This tool doesn't have any concept pairs configured.</p>
      </div>
    );
  }

  if (isCompleted && showResults) {
    const timeSpent = Math.round((new Date().getTime() - startTime.getTime()) / 60000);
    
    return (
      <div className="text-center py-12">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="w-16 h-16 bg-[var(--emerald)]/10 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <CheckCircle2 className="w-8 h-8 text-[var(--emerald)]" />
        </motion.div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">Matching Completed!</h3>
        <p className="text-[var(--grey)] mb-6">
          You matched {correctCount} out of {totalPairs} pairs correctly ({accuracy}% accuracy)
          {timeSpent > 0 && ` in ${timeSpent} minutes`}.
        </p>
        
        <div className="grid grid-cols-3 gap-4 max-w-md mx-auto mb-6">
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--emerald)]">{correctCount}</div>
            <div className="text-sm text-[var(--grey)]">Correct</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-red-500">{totalPairs - correctCount}</div>
            <div className="text-sm text-[var(--grey)]">Incorrect</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--bright-green)]">{accuracy}%</div>
            <div className="text-sm text-[var(--grey)]">Accuracy</div>
          </div>
        </div>
        
        <Button onClick={handleReset} variant="outline">
          <RotateCcw className="w-4 h-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-3xl font-bold text-[var(--charcoal)] mb-2"
        >
          {tool.name}
        </motion.h2>
        <p className="text-[var(--grey)] text-lg">{tool.description}</p>
        <div className="flex items-center justify-center gap-2 mt-4 text-sm text-[var(--grey)]">
          <Target className="w-4 h-4" />
          Click on a concept, then click on its matching definition
        </div>
      </div>

      {/* Progress */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-[var(--charcoal)]">
            Progress: {matchedCount}/{totalPairs} pairs matched
          </span>
          <div className="flex items-center gap-4 text-sm text-[var(--grey)]">
            <div className="flex items-center gap-1">
              <CheckCircle2 className="w-4 h-4" />
              {correctCount} correct
            </div>
            <div className="flex items-center gap-1">
              <X className="w-4 h-4" />
              {incorrectMatches.size} incorrect
            </div>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className="bg-[var(--emerald)] h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-center gap-4 mb-8">
        <Button onClick={handleShuffle} variant="outline" size="sm">
          <Shuffle className="w-4 h-4 mr-2" />
          Shuffle
        </Button>
        <Button onClick={handleReset} variant="outline" size="sm">
          <RotateCcw className="w-4 h-4 mr-2" />
          Reset
        </Button>
      </div>

      {/* Matching Interface */}
      <div className="grid lg:grid-cols-2 gap-8">
        {/* Concepts Column */}
        <div>
          <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-4 text-center">
            Concepts
          </h3>
          <div className="space-y-3">
            {concepts.map((concept, index) => {
              const isMatched = concept in matches;
              const isSelected = selectedConcept === concept;
              const isCorrect = correctMatches.has(concept);
              const isIncorrect = incorrectMatches.has(concept);
              
              return (
                <motion.button
                  key={concept}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => handleConceptClick(concept)}
                  disabled={isMatched}
                  className={cn(
                    "w-full p-4 rounded-xl text-left transition-all duration-200",
                    "border-2 font-medium",
                    isSelected && "ring-2 ring-blue-300 scale-105",
                    isCorrect && "bg-[var(--emerald)]/10 border-[var(--emerald)] text-[var(--emerald)]",
                    isIncorrect && "bg-red-50 border-red-300 text-red-600",
                    !isMatched && !isSelected && "bg-white border-[var(--color-border)] hover:border-[var(--emerald)] hover:bg-[var(--emerald)]/5",
                    isMatched && !isCorrect && !isIncorrect && "bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed"
                  )}
                >
                  <div className="flex items-center justify-between">
                    <span>{concept}</span>
                    {isCorrect && <CheckCircle2 className="w-5 h-5" />}
                    {isIncorrect && <X className="w-5 h-5" />}
                  </div>
                </motion.button>
              );
            })}
          </div>
        </div>

        {/* Definitions Column */}
        <div>
          <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-4 text-center">
            Definitions
          </h3>
          <div className="space-y-3">
            {definitions.map((definition, index) => {
              const isMatched = Object.values(matches).includes(definition);
              const isSelected = selectedDefinition === definition;
              const matchedConcept = Object.entries(matches).find(([_, def]) => def === definition)?.[0];
              const isCorrect = matchedConcept && correctMatches.has(matchedConcept);
              const isIncorrect = matchedConcept && incorrectMatches.has(matchedConcept);
              
              return (
                <motion.button
                  key={definition}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => handleDefinitionClick(definition)}
                  disabled={isMatched}
                  className={cn(
                    "w-full p-4 rounded-xl text-left transition-all duration-200",
                    "border-2",
                    isSelected && "ring-2 ring-blue-300 scale-105",
                    isCorrect && "bg-[var(--emerald)]/10 border-[var(--emerald)] text-[var(--emerald)]",
                    isIncorrect && "bg-red-50 border-red-300 text-red-600",
                    !isMatched && !isSelected && "bg-white border-[var(--color-border)] hover:border-[var(--emerald)] hover:bg-[var(--emerald)]/5",
                    isMatched && !isCorrect && !isIncorrect && "bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed"
                  )}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm leading-relaxed">{definition}</span>
                    {isCorrect && <CheckCircle2 className="w-5 h-5 flex-shrink-0 ml-2" />}
                    {isIncorrect && <X className="w-5 h-5 flex-shrink-0 ml-2" />}
                  </div>
                </motion.button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Connection Lines */}
      <div className="mt-8">
        <svg className="w-full h-32 absolute pointer-events-none" style={{ zIndex: -1 }}>
          {Object.entries(matches).map(([concept, definition]) => {
            const conceptIndex = concepts.indexOf(concept);
            const definitionIndex = definitions.indexOf(definition);
            const isCorrect = correctMatches.has(concept);
            
            if (conceptIndex === -1 || definitionIndex === -1) return null;
            
            return (
              <motion.line
                key={`${concept}-${definition}`}
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{ pathLength: 1, opacity: 0.6 }}
                transition={{ duration: 0.5 }}
                x1="25%"
                y1={`${(conceptIndex + 1) * (100 / (concepts.length + 1))}%`}
                x2="75%"
                y2={`${(definitionIndex + 1) * (100 / (definitions.length + 1))}%`}
                stroke={isCorrect ? "var(--emerald)" : "#ef4444"}
                strokeWidth="2"
                strokeDasharray={isCorrect ? "none" : "5,5"}
              />
            );
          })}
        </svg>
      </div>

      {/* Instructions */}
      <div className="mt-8 text-center">
        <div className="inline-flex items-center gap-4 px-6 py-3 bg-white rounded-xl border border-[var(--color-border)] text-sm text-[var(--grey)]">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-[var(--emerald)] rounded-full"></div>
            <span>Correct Match</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span>Incorrect Match</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>Selected</span>
          </div>
        </div>
      </div>
    </div>
  );
}
