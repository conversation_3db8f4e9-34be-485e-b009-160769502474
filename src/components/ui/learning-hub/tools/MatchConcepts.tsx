"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Check<PERSON>ir<PERSON>, RotateCcw, Shuffle, Eye, EyeOff, Target } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ConceptPair {
  id: string;
  concept: string;
  definition: string;
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
}

interface MatchConceptsProps {
  content: {
    pairs: ConceptPair[];
    title?: string;
    description?: string;
  };
  onProgressUpdate: (progress: number, completed?: boolean) => void;
  progress: number;
  isCompleted: boolean;
}

export default function MatchConcepts({ content, onProgressUpdate, progress, isCompleted }: MatchConceptsProps) {
  const [concepts, setConcepts] = useState<string[]>([]);
  const [definitions, setDefinitions] = useState<string[]>([]);
  const [selectedConcept, setSelectedConcept] = useState<string | null>(null);
  const [selectedDefinition, setSelectedDefinition] = useState<string | null>(null);
  const [matches, setMatches] = useState<Map<string, string>>(new Map());
  const [correctMatches, setCorrectMatches] = useState<Set<string>>(new Set());
  const [incorrectMatches, setIncorrectMatches] = useState<Set<string>>(new Set());
  const [showAnswers, setShowAnswers] = useState(false);
  const [gameCompleted, setGameCompleted] = useState(false);
  const [score, setScore] = useState(0);

  const pairs = content?.pairs || [];

  useEffect(() => {
    if (pairs.length > 0) {
      const shuffledConcepts = [...pairs.map(p => p.concept)].sort(() => Math.random() - 0.5);
      const shuffledDefinitions = [...pairs.map(p => p.definition)].sort(() => Math.random() - 0.5);
      setConcepts(shuffledConcepts);
      setDefinitions(shuffledDefinitions);
    }
  }, [pairs]);

  useEffect(() => {
    if (matches.size > 0) {
      const newProgress = Math.round((matches.size / pairs.length) * 100);
      const completed = matches.size === pairs.length;
      
      if (completed && !gameCompleted) {
        checkAllMatches();
        setGameCompleted(true);
      }
      
      onProgressUpdate(newProgress, completed);
    }
  }, [matches, pairs.length, onProgressUpdate, gameCompleted]);

  const handleConceptClick = (concept: string) => {
    if (correctMatches.has(concept) || incorrectMatches.has(concept)) return;
    
    setSelectedConcept(selectedConcept === concept ? null : concept);
    setSelectedDefinition(null);
  };

  const handleDefinitionClick = (definition: string) => {
    if (Array.from(matches.values()).includes(definition)) return;
    
    if (selectedConcept) {
      setMatches(prev => new Map(prev.set(selectedConcept, definition)));
      setSelectedConcept(null);
      setSelectedDefinition(null);
    } else {
      setSelectedDefinition(selectedDefinition === definition ? null : definition);
      setSelectedConcept(null);
    }
  };

  const checkAllMatches = () => {
    const correct = new Set<string>();
    const incorrect = new Set<string>();
    let correctCount = 0;

    matches.forEach((definition, concept) => {
      const pair = pairs.find(p => p.concept === concept);
      if (pair && pair.definition === definition) {
        correct.add(concept);
        correctCount++;
      } else {
        incorrect.add(concept);
      }
    });

    setCorrectMatches(correct);
    setIncorrectMatches(incorrect);
    setScore(Math.round((correctCount / pairs.length) * 100));
  };

  const handleShuffle = () => {
    const shuffledConcepts = [...concepts].sort(() => Math.random() - 0.5);
    const shuffledDefinitions = [...definitions].sort(() => Math.random() - 0.5);
    setConcepts(shuffledConcepts);
    setDefinitions(shuffledDefinitions);
  };

  const handleReset = () => {
    setMatches(new Map());
    setCorrectMatches(new Set());
    setIncorrectMatches(new Set());
    setSelectedConcept(null);
    setSelectedDefinition(null);
    setShowAnswers(false);
    setGameCompleted(false);
    setScore(0);
    handleShuffle();
    onProgressUpdate(0, false);
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'hard':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (pairs.length === 0) {
    return (
      <div className="p-8 text-center">
        <p className="text-[var(--grey)]">No concept matching pairs available for this lesson.</p>
      </div>
    );
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--charcoal)] mb-2">
          {content.title || "Match Concepts"}
        </h2>
        <p className="text-[var(--grey)] max-w-2xl mx-auto mb-4">
          {content.description || "Match each concept with its correct definition."}
        </p>
        <div className="flex items-center justify-center gap-2 text-sm text-[var(--emerald)]">
          <Target className="w-4 h-4" />
          <span>Click a concept, then click its matching definition</span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={handleShuffle}
            className="text-[var(--emerald)] border-[var(--emerald)]"
          >
            <Shuffle className="w-4 h-4 mr-2" />
            Shuffle
          </Button>
          <Button
            variant="outline"
            onClick={handleReset}
            className="text-[var(--grey)] border-[var(--color-border)]"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowAnswers(!showAnswers)}
            className="text-[var(--charcoal)] border-[var(--color-border)]"
          >
            {showAnswers ? <EyeOff className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
            {showAnswers ? 'Hide Answers' : 'Show Answers'}
          </Button>
        </div>

        <div className="flex items-center gap-4 text-sm text-[var(--grey)]">
          <span>{matches.size} of {pairs.length} matched</span>
          {gameCompleted && (
            <>
              <span>•</span>
              <span className={cn("font-semibold", getScoreColor(score))}>
                Score: {score}%
              </span>
            </>
          )}
        </div>
      </div>

      {/* Game Board */}
      <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
        {/* Concepts Column */}
        <div>
          <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-4 text-center">
            Concepts
          </h3>
          <div className="space-y-3">
            {concepts.map((concept, index) => {
              const pair = pairs.find(p => p.concept === concept);
              const isMatched = matches.has(concept);
              const isCorrect = correctMatches.has(concept);
              const isIncorrect = incorrectMatches.has(concept);
              const isSelected = selectedConcept === concept;
              
              return (
                <motion.button
                  key={concept}
                  onClick={() => handleConceptClick(concept)}
                  disabled={isMatched && gameCompleted}
                  className={cn(
                    "w-full p-4 text-left rounded-xl border-2 transition-all duration-200",
                    "focus:outline-none focus:ring-2 focus:ring-[var(--emerald)]/50",
                    isSelected && "border-[var(--emerald)] bg-[var(--emerald)]/10 scale-105",
                    isCorrect && "border-green-500 bg-green-50",
                    isIncorrect && "border-red-500 bg-red-50",
                    !isMatched && !isSelected && "border-[var(--color-border)] hover:border-[var(--emerald)] hover:bg-[var(--emerald)]/5",
                    isMatched && !gameCompleted && "border-blue-300 bg-blue-50",
                    showAnswers && "bg-yellow-50 border-yellow-300"
                  )}
                  whileHover={!isMatched || !gameCompleted ? { scale: 1.02 } : {}}
                  whileTap={!isMatched || !gameCompleted ? { scale: 0.98 } : {}}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-[var(--charcoal)] mb-1">
                        {concept}
                      </div>
                      {pair?.category && (
                        <div className="text-xs text-[var(--grey)] mb-2">
                          {pair.category}
                        </div>
                      )}
                      {showAnswers && (
                        <div className="text-sm text-yellow-700 bg-yellow-100 rounded p-2 mt-2">
                          <strong>Answer:</strong> {pair?.definition}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex flex-col items-end gap-2">
                      {pair?.difficulty && (
                        <div className={cn(
                          "px-2 py-1 rounded-full text-xs font-medium border",
                          getDifficultyColor(pair.difficulty)
                        )}>
                          {pair.difficulty}
                        </div>
                      )}
                      
                      {isCorrect && (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      )}
                      {isIncorrect && (
                        <div className="w-5 h-5 rounded-full bg-red-600 flex items-center justify-center">
                          <span className="text-white text-xs">✕</span>
                        </div>
                      )}
                      {isMatched && !gameCompleted && (
                        <div className="w-5 h-5 rounded-full bg-blue-600 flex items-center justify-center">
                          <span className="text-white text-xs">?</span>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.button>
              );
            })}
          </div>
        </div>

        {/* Definitions Column */}
        <div>
          <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-4 text-center">
            Definitions
          </h3>
          <div className="space-y-3">
            {definitions.map((definition, index) => {
              const isMatched = Array.from(matches.values()).includes(definition);
              const matchedConcept = Array.from(matches.entries()).find(([_, def]) => def === definition)?.[0];
              const isCorrect = matchedConcept && correctMatches.has(matchedConcept);
              const isIncorrect = matchedConcept && incorrectMatches.has(matchedConcept);
              const isSelected = selectedDefinition === definition;
              
              return (
                <motion.button
                  key={definition}
                  onClick={() => handleDefinitionClick(definition)}
                  disabled={isMatched}
                  className={cn(
                    "w-full p-4 text-left rounded-xl border-2 transition-all duration-200",
                    "focus:outline-none focus:ring-2 focus:ring-[var(--emerald)]/50",
                    isSelected && "border-[var(--emerald)] bg-[var(--emerald)]/10 scale-105",
                    isCorrect && "border-green-500 bg-green-50",
                    isIncorrect && "border-red-500 bg-red-50",
                    !isMatched && !isSelected && "border-[var(--color-border)] hover:border-[var(--emerald)] hover:bg-[var(--emerald)]/5",
                    isMatched && !gameCompleted && "border-blue-300 bg-blue-50",
                    isMatched && "cursor-not-allowed opacity-75"
                  )}
                  whileHover={!isMatched ? { scale: 1.02 } : {}}
                  whileTap={!isMatched ? { scale: 0.98 } : {}}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="text-[var(--charcoal)] leading-relaxed">
                        {definition}
                      </div>
                      {matchedConcept && (
                        <div className="text-sm text-blue-600 mt-2 font-medium">
                          Matched with: {matchedConcept}
                        </div>
                      )}
                    </div>
                    
                    <div className="ml-4">
                      {isCorrect && (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      )}
                      {isIncorrect && (
                        <div className="w-5 h-5 rounded-full bg-red-600 flex items-center justify-center">
                          <span className="text-white text-xs">✕</span>
                        </div>
                      )}
                      {isMatched && !gameCompleted && (
                        <div className="w-5 h-5 rounded-full bg-blue-600 flex items-center justify-center">
                          <span className="text-white text-xs">?</span>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Results */}
      {gameCompleted && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mt-8 p-6 bg-gradient-to-r from-[var(--emerald)]/10 to-[var(--bright-green)]/10 rounded-2xl border border-[var(--emerald)]/20"
        >
          <div className="text-4xl mb-4">
            {score >= 80 ? '🎯' : score >= 60 ? '👍' : '📚'}
          </div>
          <h3 className="text-xl font-semibold text-[var(--charcoal)] mb-2">
            Matching Complete!
          </h3>
          <div className={cn("text-3xl font-bold mb-4", getScoreColor(score))}>
            {score}%
          </div>
          <p className="text-[var(--grey)] mb-6">
            You correctly matched {correctMatches.size} out of {pairs.length} concepts.
          </p>
          
          {/* Score Breakdown */}
          <div className="grid md:grid-cols-3 gap-4 max-w-md mx-auto">
            <div className="bg-green-50 rounded-xl p-4 border border-green-200">
              <div className="text-2xl font-bold text-green-600">
                {correctMatches.size}
              </div>
              <div className="text-green-700 text-sm">Correct</div>
            </div>
            <div className="bg-red-50 rounded-xl p-4 border border-red-200">
              <div className="text-2xl font-bold text-red-600">
                {incorrectMatches.size}
              </div>
              <div className="text-red-700 text-sm">Incorrect</div>
            </div>
            <div className="bg-blue-50 rounded-xl p-4 border border-blue-200">
              <div className="text-2xl font-bold text-blue-600">
                {pairs.length}
              </div>
              <div className="text-blue-700 text-sm">Total</div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
