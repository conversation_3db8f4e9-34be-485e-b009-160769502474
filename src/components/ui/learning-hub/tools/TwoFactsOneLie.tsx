"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CheckCircle2, X, ArrowRight, RotateCcw, AlertTriangle, Target } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { type LearningTool } from "@/Services/planService";

interface TwoFactsOneLieQuestion {
  id: string;
  topic: string;
  statements: [string, string, string]; // Two facts and one lie
  lieIndex: 0 | 1 | 2; // Index of the lie
  explanations: [string, string, string]; // Explanation for each statement
  difficulty?: "easy" | "medium" | "hard";
}

interface TwoFactsOneLieProps {
  tool: LearningTool;
  onComplete: (score?: number) => void;
  isCompleted: boolean;
}

export default function TwoFactsOneLie({ tool, onComplete, isCompleted }: TwoFactsOneLieProps) {
  const questions: TwoFactsOneLieQuestion[] = tool.content?.questions || [];
  
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedStatement, setSelectedStatement] = useState<number | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);
  const [answers, setAnswers] = useState<{ [key: string]: number }>({});
  const [startTime, setStartTime] = useState<Date>(new Date());

  const currentQuestion = questions[currentIndex];
  const totalQuestions = questions.length;
  const answeredCount = Object.keys(answers).length;
  const correctCount = Object.entries(answers).filter(([id, userAnswer]) => {
    const question = questions.find(q => q.id === id);
    return question && question.lieIndex === userAnswer;
  }).length;
  
  const progressPercentage = totalQuestions > 0 ? Math.round((answeredCount / totalQuestions) * 100) : 0;
  const accuracy = answeredCount > 0 ? Math.round((correctCount / answeredCount) * 100) : 0;

  useEffect(() => {
    setStartTime(new Date());
  }, []);

  const handleStatementSelect = (index: number) => {
    if (showExplanation) return;
    
    setSelectedStatement(index);
    setShowExplanation(true);
    
    // Record the answer
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: index
    }));
  };

  const handleNext = () => {
    if (currentIndex < totalQuestions - 1) {
      setCurrentIndex(prev => prev + 1);
      setSelectedStatement(null);
      setShowExplanation(false);
    } else {
      // Completed all questions
      const finalScore = Math.round((correctCount / totalQuestions) * 100);
      onComplete(finalScore);
    }
  };

  const handleReset = () => {
    setCurrentIndex(0);
    setSelectedStatement(null);
    setShowExplanation(false);
    setAnswers({});
    setStartTime(new Date());
  };

  if (totalQuestions === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <X className="w-8 h-8 text-red-600" />
        </div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">No Questions Available</h3>
        <p className="text-[var(--grey)]">This tool doesn't have any Two Facts One Lie questions configured.</p>
      </div>
    );
  }

  if (isCompleted) {
    const timeSpent = Math.round((new Date().getTime() - startTime.getTime()) / 60000);
    
    return (
      <div className="text-center py-12">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="w-16 h-16 bg-[var(--emerald)]/10 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <CheckCircle2 className="w-8 h-8 text-[var(--emerald)]" />
        </motion.div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">Two Facts One Lie Completed!</h3>
        <p className="text-[var(--grey)] mb-6">
          You identified {correctCount} out of {totalQuestions} lies correctly ({accuracy}% accuracy)
          {timeSpent > 0 && ` in ${timeSpent} minutes`}.
        </p>
        
        <div className="grid grid-cols-3 gap-4 max-w-md mx-auto mb-6">
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--emerald)]">{correctCount}</div>
            <div className="text-sm text-[var(--grey)]">Lies Found</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-red-500">{totalQuestions - correctCount}</div>
            <div className="text-sm text-[var(--grey)]">Missed</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--bright-green)]">{accuracy}%</div>
            <div className="text-sm text-[var(--grey)]">Accuracy</div>
          </div>
        </div>
        
        <Button onClick={handleReset} variant="outline">
          <RotateCcw className="w-4 h-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  const isCorrect = selectedStatement === currentQuestion?.lieIndex;

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-3xl font-bold text-[var(--charcoal)] mb-2"
        >
          {tool.name}
        </motion.h2>
        <p className="text-[var(--grey)] text-lg">{tool.description}</p>
        <div className="flex items-center justify-center gap-2 mt-4 text-sm text-[var(--grey)]">
          <AlertTriangle className="w-4 h-4" />
          Find the lie among the three statements
        </div>
      </div>

      {/* Progress */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-[var(--charcoal)]">
            Question {currentIndex + 1} of {totalQuestions}
          </span>
          <div className="flex items-center gap-4 text-sm text-[var(--grey)]">
            <div className="flex items-center gap-1">
              <Target className="w-4 h-4" />
              {accuracy}% accuracy
            </div>
            <div className="flex items-center gap-1">
              <AlertTriangle className="w-4 h-4" />
              {correctCount}/{answeredCount} lies found
            </div>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className="bg-[var(--emerald)] h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      {/* Question Card */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentQuestion?.id}
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -50 }}
          transition={{ duration: 0.3 }}
          className="bg-white rounded-2xl p-8 shadow-lg border border-[var(--color-border)] mb-8"
        >
          {/* Topic */}
          <div className="text-center mb-8">
            <div className="inline-block px-4 py-2 bg-[var(--emerald)]/10 text-[var(--emerald)] rounded-full text-sm font-medium mb-4">
              {currentQuestion?.topic}
            </div>
            <h3 className="text-lg font-semibold text-[var(--charcoal)]">
              Which statement is the lie?
            </h3>
          </div>

          {/* Statements */}
          {!showExplanation && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              {currentQuestion?.statements.map((statement, index) => (
                <Button
                  key={index}
                  onClick={() => handleStatementSelect(index)}
                  variant="outline"
                  className={cn(
                    "w-full p-6 h-auto text-left justify-start",
                    "border-2 hover:border-[var(--emerald)] hover:bg-[var(--emerald)]/5",
                    "transform hover:scale-[1.02] transition-all duration-200"
                  )}
                >
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 rounded-full bg-[var(--emerald)]/10 text-[var(--emerald)] flex items-center justify-center font-semibold text-sm">
                      {String.fromCharCode(65 + index)}
                    </div>
                    <div className="flex-1 text-[var(--charcoal)] leading-relaxed">
                      {statement}
                    </div>
                  </div>
                </Button>
              ))}
            </motion.div>
          )}

          {/* Explanation */}
          {showExplanation && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              {/* Result */}
              <div className={cn(
                "flex items-center justify-center gap-3 p-4 rounded-xl",
                isCorrect 
                  ? "bg-[var(--emerald)]/10 text-[var(--emerald)]" 
                  : "bg-red-50 text-red-600"
              )}>
                {isCorrect ? (
                  <CheckCircle2 className="w-6 h-6" />
                ) : (
                  <X className="w-6 h-6" />
                )}
                <span className="font-semibold text-lg">
                  {isCorrect ? 'Correct! You found the lie!' : 'Incorrect. Try again next time!'}
                </span>
              </div>

              {/* Statements with explanations */}
              <div className="space-y-4">
                {currentQuestion?.statements.map((statement, index) => {
                  const isLie = index === currentQuestion.lieIndex;
                  const wasSelected = index === selectedStatement;
                  
                  return (
                    <div
                      key={index}
                      className={cn(
                        "p-4 rounded-xl border-2",
                        isLie 
                          ? "border-red-200 bg-red-50" 
                          : "border-[var(--emerald)]/20 bg-[var(--emerald)]/5",
                        wasSelected && "ring-2 ring-blue-200"
                      )}
                    >
                      <div className="flex items-start gap-3 mb-3">
                        <div className={cn(
                          "w-6 h-6 rounded-full flex items-center justify-center text-sm font-semibold",
                          isLie ? "bg-red-500 text-white" : "bg-[var(--emerald)] text-white"
                        )}>
                          {isLie ? <X className="w-3 h-3" /> : <CheckCircle2 className="w-3 h-3" />}
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-[var(--charcoal)] mb-1">
                            {String.fromCharCode(65 + index)}. {statement}
                          </p>
                          <p className={cn(
                            "text-sm font-medium",
                            isLie ? "text-red-600" : "text-[var(--emerald)]"
                          )}>
                            {isLie ? "LIE" : "FACT"} {wasSelected && "(Your choice)"}
                          </p>
                        </div>
                      </div>
                      <div className="bg-white/50 rounded-lg p-3">
                        <p className="text-sm text-[var(--grey)] leading-relaxed">
                          {currentQuestion?.explanations[index]}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Next Button */}
              <div className="text-center">
                <Button
                  onClick={handleNext}
                  className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white px-8 py-3"
                >
                  {currentIndex === totalQuestions - 1 ? 'Complete' : 'Next Question'}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </motion.div>
          )}
        </motion.div>
      </AnimatePresence>

      {/* Question Navigation */}
      <div className="flex items-center justify-center gap-2">
        {questions.map((_, index) => {
          const questionId = questions[index].id;
          const isAnswered = questionId in answers;
          const isCurrentQuestion = index === currentIndex;
          
          return (
            <div
              key={index}
              className={cn(
                "w-3 h-3 rounded-full transition-colors",
                isCurrentQuestion
                  ? "bg-[var(--emerald)] scale-125"
                  : isAnswered
                  ? answers[questionId] === questions[index].lieIndex
                    ? "bg-[var(--bright-green)]"
                    : "bg-red-400"
                  : "bg-gray-300"
              )}
            />
          );
        })}
      </div>
    </div>
  );
}
