"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronRight, ChevronLeft, CheckCircle, BookOpen, Lightbulb, Target, RotateCcw, Eye, EyeOff } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface LearningStep {
  id: string;
  title: string;
  content: string;
  keyPoints: string[];
  examples?: string[];
  tips?: string[];
  difficulty?: "easy" | "medium" | "hard";
  estimatedTime?: string;
}

interface ConceptsGuidedLearningProps {
  content: {
    steps: LearningStep[];
    title?: string;
    description?: string;
    learningObjectives?: string[];
  };
  onProgressUpdate: (progress: number, completed?: boolean) => void;
  progress: number;
  isCompleted: boolean;
}

export default function ConceptsGuidedLearning({ content, onProgressUpdate, progress, isCompleted }: ConceptsGuidedLearningProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [showKeyPoints, setShowKeyPoints] = useState(true);
  const [showExamples, setShowExamples] = useState(false);
  const [showTips, setShowTips] = useState(false);
  const [readingTime, setReadingTime] = useState(0);

  const steps = content?.steps || [];
  const currentStep = steps[currentStepIndex];

  useEffect(() => {
    if (completedSteps.size > 0) {
      const newProgress = Math.round((completedSteps.size / steps.length) * 100);
      const completed = completedSteps.size === steps.length;
      onProgressUpdate(newProgress, completed);
    }
  }, [completedSteps, steps.length, onProgressUpdate]);

  useEffect(() => {
    // Reset reading time when step changes
    setReadingTime(0);
    const interval = setInterval(() => {
      setReadingTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [currentStepIndex]);

  const handleNext = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  const handleStepComplete = () => {
    setCompletedSteps(prev => new Set([...prev, currentStepIndex]));
    if (currentStepIndex < steps.length - 1) {
      setTimeout(() => {
        handleNext();
      }, 500);
    }
  };

  const handleReset = () => {
    setCurrentStepIndex(0);
    setCompletedSteps(new Set());
    setShowKeyPoints(true);
    setShowExamples(false);
    setShowTips(false);
    setReadingTime(0);
    onProgressUpdate(0, false);
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'hard':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (steps.length === 0) {
    return (
      <div className="p-8 text-center">
        <p className="text-[var(--grey)]">No guided learning steps available for this lesson.</p>
      </div>
    );
  }

  const isStepCompleted = completedSteps.has(currentStepIndex);

  return (
    <div className="p-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--charcoal)] mb-2">
          {content.title || "Concepts Guided Learning"}
        </h2>
        {content.description && (
          <p className="text-[var(--grey)] max-w-2xl mx-auto">
            {content.description}
          </p>
        )}
      </div>

      {/* Learning Objectives */}
      {content.learningObjectives && content.learningObjectives.length > 0 && (
        <div className="max-w-4xl mx-auto mb-8">
          <div className="bg-blue-50 border border-blue-200 rounded-2xl p-6">
            <div className="flex items-start gap-3">
              <Target className="w-6 h-6 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-blue-800 mb-3">Learning Objectives</h3>
                <ul className="space-y-2">
                  {content.learningObjectives.map((objective, index) => (
                    <li key={index} className="flex items-start gap-2 text-blue-700">
                      <div className="w-2 h-2 rounded-full bg-blue-600 mt-2 flex-shrink-0" />
                      <span className="text-sm">{objective}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Progress */}
      <div className="max-w-4xl mx-auto mb-8">
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm text-[var(--grey)]">
            Step {currentStepIndex + 1} of {steps.length}
          </span>
          <div className="flex items-center gap-4 text-sm text-[var(--grey)]">
            <span>Reading time: {formatTime(readingTime)}</span>
            <span>•</span>
            <span>{completedSteps.size} completed</span>
          </div>
        </div>
        
        {/* Step Progress Indicators */}
        <div className="flex justify-center mb-4">
          <div className="flex gap-2">
            {steps.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentStepIndex(index)}
                className={cn(
                  "w-4 h-4 rounded-full transition-all duration-200",
                  index === currentStepIndex
                    ? "bg-[var(--emerald)] scale-125"
                    : completedSteps.has(index)
                    ? "bg-[var(--bright-green)]"
                    : "bg-gray-200 hover:bg-gray-300"
                )}
              />
            ))}
          </div>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-[var(--emerald)] h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStepIndex + 1) / steps.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between mb-8 max-w-4xl mx-auto">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={handleReset}
            className="text-[var(--grey)] border-[var(--color-border)]"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowKeyPoints(!showKeyPoints)}
            className={cn(
              "text-xs",
              showKeyPoints ? "bg-[var(--emerald)] text-white" : "text-[var(--charcoal)] border-[var(--color-border)]"
            )}
          >
            Key Points
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowExamples(!showExamples)}
            className={cn(
              "text-xs",
              showExamples ? "bg-[var(--emerald)] text-white" : "text-[var(--charcoal)] border-[var(--color-border)]"
            )}
          >
            Examples
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowTips(!showTips)}
            className={cn(
              "text-xs",
              showTips ? "bg-[var(--emerald)] text-white" : "text-[var(--charcoal)] border-[var(--color-border)]"
            )}
          >
            Tips
          </Button>
        </div>
      </div>

      {/* Current Step */}
      <div className="max-w-4xl mx-auto">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStepIndex}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-2xl p-8 shadow-lg border border-[var(--color-border)] mb-8"
          >
            {/* Step Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 rounded-full bg-[var(--emerald)] text-white flex items-center justify-center font-semibold">
                  {currentStepIndex + 1}
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[var(--charcoal)]">
                    {currentStep.title}
                  </h3>
                  {currentStep.estimatedTime && (
                    <p className="text-sm text-[var(--grey)]">
                      Estimated time: {currentStep.estimatedTime}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                {currentStep.difficulty && (
                  <div className={cn(
                    "px-3 py-1 rounded-full text-xs font-medium border",
                    getDifficultyColor(currentStep.difficulty)
                  )}>
                    {currentStep.difficulty}
                  </div>
                )}
                
                {isStepCompleted && (
                  <div className="flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 border border-green-200">
                    <CheckCircle className="w-4 h-4" />
                    Completed
                  </div>
                )}
              </div>
            </div>

            {/* Step Content */}
            <div className="prose prose-lg max-w-none mb-8">
              <div className="text-[var(--charcoal)] leading-relaxed whitespace-pre-line">
                {currentStep.content}
              </div>
            </div>

            {/* Key Points */}
            {showKeyPoints && currentStep.keyPoints.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-[var(--emerald)]/5 border border-[var(--emerald)]/20 rounded-xl p-6 mb-6"
              >
                <div className="flex items-start gap-3">
                  <BookOpen className="w-5 h-5 text-[var(--emerald)] mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--emerald)] mb-3">Key Points</h4>
                    <ul className="space-y-2">
                      {currentStep.keyPoints.map((point, index) => (
                        <li key={index} className="flex items-start gap-2 text-[var(--charcoal)]">
                          <div className="w-2 h-2 rounded-full bg-[var(--emerald)] mt-2 flex-shrink-0" />
                          <span className="text-sm">{point}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Examples */}
            {showExamples && currentStep.examples && currentStep.examples.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-6"
              >
                <div className="flex items-start gap-3">
                  <Target className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-blue-800 mb-3">Examples</h4>
                    <div className="space-y-3">
                      {currentStep.examples.map((example, index) => (
                        <div key={index} className="bg-white rounded-lg p-4 border border-blue-200">
                          <div className="text-sm text-blue-700">{example}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Tips */}
            {showTips && currentStep.tips && currentStep.tips.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-6"
              >
                <div className="flex items-start gap-3">
                  <Lightbulb className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-yellow-800 mb-3">Tips</h4>
                    <ul className="space-y-2">
                      {currentStep.tips.map((tip, index) => (
                        <li key={index} className="flex items-start gap-2 text-yellow-700">
                          <div className="w-2 h-2 rounded-full bg-yellow-600 mt-2 flex-shrink-0" />
                          <span className="text-sm">{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Step Actions */}
            <div className="flex items-center justify-between pt-6 border-t border-[var(--color-border)]">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStepIndex === 0}
                className="text-[var(--charcoal)] border-[var(--color-border)]"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>

              <div className="flex items-center gap-3">
                {!isStepCompleted && (
                  <Button
                    onClick={handleStepComplete}
                    className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Mark Complete
                  </Button>
                )}

                {currentStepIndex < steps.length - 1 ? (
                  <Button
                    onClick={handleNext}
                    variant={isStepCompleted ? "default" : "outline"}
                    className={isStepCompleted ? "bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white" : ""}
                  >
                    Next Step
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </Button>
                ) : isStepCompleted && (
                  <div className="flex items-center gap-2 text-[var(--emerald)]">
                    <CheckCircle className="w-5 h-5" />
                    <span className="font-medium">All Steps Completed!</span>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Completion Message */}
      {isCompleted && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mt-8 p-6 bg-gradient-to-r from-[var(--emerald)]/10 to-[var(--bright-green)]/10 rounded-2xl border border-[var(--emerald)]/20 max-w-4xl mx-auto"
        >
          <div className="text-4xl mb-4">📚</div>
          <h3 className="text-xl font-semibold text-[var(--charcoal)] mb-2">
            Guided Learning Complete!
          </h3>
          <p className="text-[var(--grey)]">
            You've successfully completed all {steps.length} learning steps. Great job!
          </p>
        </motion.div>
      )}
    </div>
  );
}
