"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CheckCircle2, ArrowRight, ArrowLeft, BookOpen, Lightbulb, Target, RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { type LearningTool } from "@/Services/planService";

interface ConceptStep {
  id: string;
  title: string;
  content: string;
  type: "introduction" | "explanation" | "example" | "practice" | "summary";
  keyPoints?: string[];
  examples?: string[];
  practiceQuestion?: {
    question: string;
    options: string[];
    correctIndex: number;
    explanation: string;
  };
  tips?: string[];
}

interface ConceptLearningPath {
  id: string;
  title: string;
  description: string;
  difficulty: "beginner" | "intermediate" | "advanced";
  estimatedTime: string;
  steps: ConceptStep[];
  learningObjectives: string[];
}

interface ConceptsGuidedLearningProps {
  tool: LearningTool;
  onComplete: (score?: number) => void;
  isCompleted: boolean;
}

export default function ConceptsGuidedLearning({ tool, onComplete, isCompleted }: ConceptsGuidedLearningProps) {
  const learningPath: ConceptLearningPath = tool.content?.learningPath || { 
    id: "", title: "", description: "", difficulty: "beginner", estimatedTime: "", steps: [], learningObjectives: [] 
  };
  
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [practiceAnswers, setPracticeAnswers] = useState<{ [key: string]: number }>({});
  const [showPracticeResult, setShowPracticeResult] = useState<{ [key: string]: boolean }>({});
  const [startTime, setStartTime] = useState<Date>(new Date());

  const currentStep = learningPath.steps[currentStepIndex];
  const totalSteps = learningPath.steps.length;
  const completedCount = completedSteps.size;
  const progressPercentage = totalSteps > 0 ? Math.round((completedCount / totalSteps) * 100) : 0;

  const practiceSteps = learningPath.steps.filter(step => step.type === "practice");
  const practiceAnswersCount = Object.keys(practiceAnswers).length;
  const correctPracticeAnswers = Object.entries(practiceAnswers).filter(([stepId, answerIndex]) => {
    const step = learningPath.steps.find(s => s.id === stepId);
    return step?.practiceQuestion?.correctIndex === answerIndex;
  }).length;

  useEffect(() => {
    setStartTime(new Date());
  }, []);

  const handleStepComplete = () => {
    if (currentStep) {
      setCompletedSteps(prev => new Set([...prev, currentStep.id]));
      
      // Auto-advance to next step
      if (currentStepIndex < totalSteps - 1) {
        setTimeout(() => {
          setCurrentStepIndex(prev => prev + 1);
        }, 500);
      } else {
        // Completed all steps
        const practiceScore = practiceSteps.length > 0 
          ? Math.round((correctPracticeAnswers / practiceSteps.length) * 100)
          : 100;
        onComplete(practiceScore);
      }
    }
  };

  const handlePracticeAnswer = (answerIndex: number) => {
    if (!currentStep?.practiceQuestion) return;
    
    setPracticeAnswers(prev => ({
      ...prev,
      [currentStep.id]: answerIndex
    }));
    
    setShowPracticeResult(prev => ({
      ...prev,
      [currentStep.id]: true
    }));
    
    // Auto-complete step after showing result
    setTimeout(() => {
      handleStepComplete();
    }, 2000);
  };

  const handleNext = () => {
    if (currentStepIndex < totalSteps - 1) {
      setCurrentStepIndex(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
    }
  };

  const handleReset = () => {
    setCurrentStepIndex(0);
    setCompletedSteps(new Set());
    setPracticeAnswers({});
    setShowPracticeResult({});
    setStartTime(new Date());
  };

  const getStepIcon = (type: ConceptStep["type"]) => {
    switch (type) {
      case "introduction":
        return <BookOpen className="w-5 h-5" />;
      case "explanation":
        return <Lightbulb className="w-5 h-5" />;
      case "example":
        return <Target className="w-5 h-5" />;
      case "practice":
        return <CheckCircle2 className="w-5 h-5" />;
      case "summary":
        return <BookOpen className="w-5 h-5" />;
      default:
        return <BookOpen className="w-5 h-5" />;
    }
  };

  const getStepColor = (type: ConceptStep["type"]) => {
    switch (type) {
      case "introduction":
        return "var(--emerald)";
      case "explanation":
        return "var(--bright-green)";
      case "example":
        return "var(--lime-green)";
      case "practice":
        return "#f59e0b";
      case "summary":
        return "var(--emerald)";
      default:
        return "var(--grey)";
    }
  };

  if (totalSteps === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <BookOpen className="w-8 h-8 text-red-600" />
        </div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">No Learning Path Available</h3>
        <p className="text-[var(--grey)]">This tool doesn't have a guided learning path configured.</p>
      </div>
    );
  }

  if (isCompleted) {
    const timeSpent = Math.round((new Date().getTime() - startTime.getTime()) / 60000);
    const practiceScore = practiceSteps.length > 0 
      ? Math.round((correctPracticeAnswers / practiceSteps.length) * 100)
      : 100;
    
    return (
      <div className="text-center py-12">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="w-16 h-16 bg-[var(--emerald)]/10 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <CheckCircle2 className="w-8 h-8 text-[var(--emerald)]" />
        </motion.div>
        <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-2">Guided Learning Completed!</h3>
        <p className="text-[var(--grey)] mb-6">
          You've completed all {totalSteps} learning steps
          {practiceSteps.length > 0 && ` with ${practiceScore}% accuracy on practice questions`}
          {timeSpent > 0 && ` in ${timeSpent} minutes`}.
        </p>
        
        <div className="grid grid-cols-3 gap-4 max-w-md mx-auto mb-6">
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--emerald)]">{completedCount}</div>
            <div className="text-sm text-[var(--grey)]">Steps Completed</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--bright-green)]">{correctPracticeAnswers}</div>
            <div className="text-sm text-[var(--grey)]">Practice Correct</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-[var(--color-border)]">
            <div className="text-2xl font-bold text-[var(--lime-green)]">{practiceScore}%</div>
            <div className="text-sm text-[var(--grey)]">Practice Score</div>
          </div>
        </div>
        
        <Button onClick={handleReset} variant="outline">
          <RotateCcw className="w-4 h-4 mr-2" />
          Review Again
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-3xl font-bold text-[var(--charcoal)] mb-2"
        >
          {tool.name}
        </motion.h2>
        <p className="text-[var(--grey)] text-lg">{tool.description}</p>
        
        <div className="mt-4 inline-flex items-center gap-4 px-6 py-3 bg-white rounded-xl border border-[var(--color-border)]">
          <div className="flex items-center gap-2 text-sm text-[var(--grey)]">
            <Target className="w-4 h-4" />
            {learningPath.difficulty}
          </div>
          <div className="flex items-center gap-2 text-sm text-[var(--grey)]">
            <BookOpen className="w-4 h-4" />
            {learningPath.estimatedTime}
          </div>
        </div>
      </div>

      {/* Progress */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-[var(--charcoal)]">
            Step {currentStepIndex + 1} of {totalSteps}
          </span>
          <span className="text-sm text-[var(--grey)]">
            {completedCount} completed ({progressPercentage}%)
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className="bg-[var(--emerald)] h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      {/* Step Navigation */}
      <div className="flex items-center justify-center mb-8 overflow-x-auto">
        <div className="flex items-center gap-2 min-w-max px-4">
          {learningPath.steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <button
                onClick={() => setCurrentStepIndex(index)}
                disabled={index > currentStepIndex && !completedSteps.has(step.id)}
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center text-xs font-medium transition-all",
                  index === currentStepIndex
                    ? "scale-110 text-white"
                    : completedSteps.has(step.id)
                    ? "text-white hover:scale-105"
                    : index < currentStepIndex
                    ? "text-white hover:scale-105"
                    : "bg-gray-200 text-[var(--grey)] cursor-not-allowed"
                )}
                style={{
                  backgroundColor: index <= currentStepIndex || completedSteps.has(step.id) 
                    ? getStepColor(step.type) 
                    : undefined
                }}
              >
                {completedSteps.has(step.id) ? '✓' : index + 1}
              </button>
              {index < learningPath.steps.length - 1 && (
                <div className="w-8 h-0.5 bg-gray-300 mx-1" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Current Step */}
      <AnimatePresence mode="wait">
        {currentStep && (
          <motion.div
            key={currentStep.id}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-2xl p-8 shadow-lg border border-[var(--color-border)] mb-8"
          >
            {/* Step Header */}
            <div className="flex items-center gap-3 mb-6">
              <div 
                className="w-10 h-10 rounded-full flex items-center justify-center text-white"
                style={{ backgroundColor: getStepColor(currentStep.type) }}
              >
                {getStepIcon(currentStep.type)}
              </div>
              <div>
                <h3 className="text-xl font-semibold text-[var(--charcoal)]">{currentStep.title}</h3>
                <p className="text-sm text-[var(--grey)] capitalize">{currentStep.type}</p>
              </div>
            </div>

            {/* Step Content */}
            <div className="space-y-6">
              <div className="prose prose-gray max-w-none">
                <p className="text-[var(--grey)] leading-relaxed">{currentStep.content}</p>
              </div>

              {/* Key Points */}
              {currentStep.keyPoints && currentStep.keyPoints.length > 0 && (
                <div className="bg-[var(--emerald)]/5 rounded-xl p-6">
                  <h4 className="font-semibold text-[var(--charcoal)] mb-3 flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Key Points
                  </h4>
                  <ul className="space-y-2">
                    {currentStep.keyPoints.map((point, index) => (
                      <li key={index} className="flex items-start gap-2 text-[var(--grey)]">
                        <CheckCircle2 className="w-4 h-4 text-[var(--emerald)] mt-0.5 flex-shrink-0" />
                        {point}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Examples */}
              {currentStep.examples && currentStep.examples.length > 0 && (
                <div className="bg-blue-50 rounded-xl p-6">
                  <h4 className="font-semibold text-[var(--charcoal)] mb-3 flex items-center gap-2">
                    <Lightbulb className="w-5 h-5" />
                    Examples
                  </h4>
                  <div className="space-y-3">
                    {currentStep.examples.map((example, index) => (
                      <div key={index} className="bg-white rounded-lg p-4">
                        <p className="text-[var(--grey)] text-sm leading-relaxed">{example}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Practice Question */}
              {currentStep.practiceQuestion && (
                <div className="bg-yellow-50 rounded-xl p-6">
                  <h4 className="font-semibold text-[var(--charcoal)] mb-4 flex items-center gap-2">
                    <CheckCircle2 className="w-5 h-5" />
                    Practice Question
                  </h4>
                  <p className="text-[var(--grey)] mb-4 leading-relaxed">{currentStep.practiceQuestion.question}</p>
                  
                  {!showPracticeResult[currentStep.id] ? (
                    <div className="space-y-2">
                      {currentStep.practiceQuestion.options.map((option, index) => (
                        <Button
                          key={index}
                          onClick={() => handlePracticeAnswer(index)}
                          variant="outline"
                          className="w-full text-left justify-start p-4 h-auto"
                        >
                          <span className="font-medium mr-3">{String.fromCharCode(65 + index)}.</span>
                          {option}
                        </Button>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className={cn(
                        "flex items-center gap-3 p-4 rounded-xl",
                        practiceAnswers[currentStep.id] === currentStep.practiceQuestion.correctIndex
                          ? "bg-[var(--emerald)]/10 text-[var(--emerald)]"
                          : "bg-red-50 text-red-600"
                      )}>
                        {practiceAnswers[currentStep.id] === currentStep.practiceQuestion.correctIndex ? (
                          <CheckCircle2 className="w-6 h-6" />
                        ) : (
                          <BookOpen className="w-6 h-6" />
                        )}
                        <span className="font-semibold">
                          {practiceAnswers[currentStep.id] === currentStep.practiceQuestion.correctIndex 
                            ? 'Correct!' 
                            : 'Not quite right'}
                        </span>
                      </div>
                      <div className="bg-white rounded-lg p-4">
                        <p className="text-[var(--grey)] text-sm leading-relaxed">
                          {currentStep.practiceQuestion.explanation}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Tips */}
              {currentStep.tips && currentStep.tips.length > 0 && (
                <div className="bg-purple-50 rounded-xl p-6">
                  <h4 className="font-semibold text-[var(--charcoal)] mb-3 flex items-center gap-2">
                    <Lightbulb className="w-5 h-5" />
                    Tips
                  </h4>
                  <ul className="space-y-2">
                    {currentStep.tips.map((tip, index) => (
                      <li key={index} className="flex items-start gap-2 text-[var(--grey)] text-sm">
                        <Lightbulb className="w-4 h-4 text-purple-500 mt-0.5 flex-shrink-0" />
                        {tip}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Step Actions */}
            <div className="flex items-center justify-between mt-8">
              <Button
                onClick={handlePrevious}
                disabled={currentStepIndex === 0}
                variant="outline"
                className={cn(
                  "flex items-center gap-2",
                  currentStepIndex === 0 && "opacity-50 cursor-not-allowed"
                )}
              >
                <ArrowLeft className="w-4 h-4" />
                Previous
              </Button>

              {!completedSteps.has(currentStep.id) && (
                <Button
                  onClick={handleStepComplete}
                  disabled={currentStep.type === "practice" && !showPracticeResult[currentStep.id]}
                  className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
                >
                  <CheckCircle2 className="w-4 h-4 mr-2" />
                  Mark Complete
                </Button>
              )}

              <Button
                onClick={handleNext}
                disabled={currentStepIndex === totalSteps - 1}
                className={cn(
                  "flex items-center gap-2 bg-[var(--emerald)] hover:bg-[var(--emerald-deep)]",
                  currentStepIndex === totalSteps - 1 && "opacity-50 cursor-not-allowed"
                )}
              >
                Next
                <ArrowRight className="w-4 h-4" />
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Learning Objectives */}
      {learningPath.learningObjectives && learningPath.learningObjectives.length > 0 && (
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-[var(--color-border)]">
          <h4 className="font-semibold text-[var(--charcoal)] mb-4 flex items-center gap-2">
            <Target className="w-5 h-5" />
            Learning Objectives
          </h4>
          <ul className="space-y-2">
            {learningPath.learningObjectives.map((objective, index) => (
              <li key={index} className="flex items-start gap-2 text-sm text-[var(--grey)]">
                <CheckCircle2 className="w-4 h-4 text-[var(--emerald)] mt-0.5 flex-shrink-0" />
                {objective}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
