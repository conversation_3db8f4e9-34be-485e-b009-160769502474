"use client";

import { type QuestionRecord, type QuestionExplanation } from "@/Services/questionsService";
import { CheckCircle, XCircle } from "lucide-react";

interface QuestionDisplayProps {
  question: QuestionRecord;
  selectedAnswer: "A" | "B" | "C" | "D" | null;
  onAnswerSelect: (answer: "A" | "B" | "C" | "D") => void;
  showCorrectAnswer: boolean;
  isAnswered: boolean;
  analysis?: QuestionExplanation | null;
  highlightVerdicts?: boolean;
}

export default function QuestionDisplay({
  question,
  selectedAnswer,
  onAnswerSelect,
  showCorrectAnswer,
  isAnswered,
  analysis,
  highlightVerdicts = false,
}: QuestionDisplayProps) {
  const choices = [
    { key: "A" as const, text: question.choices[0] },
    { key: "B" as const, text: question.choices[1] },
    { key: "C" as const, text: question.choices[2] },
    { key: "D" as const, text: question.choices[3] },
  ];

  const getChoiceStyle = (choiceKey: "A" | "B" | "C" | "D") => {
    const isSelected = selectedAnswer === choiceKey;
    const isCorrect = question.correct === choiceKey;
    const isWrong = showCorrectAnswer && isSelected && !isCorrect;
    const shouldShowCorrect = showCorrectAnswer && isCorrect;
    const verdict = analysis?.answers.find((a) => a.key === choiceKey)?.verdict;

    let baseStyle = "flex items-start gap-4 p-4 rounded-lg border-2 cursor-pointer transition-all ";

    if (isWrong) {
      return baseStyle + "border-rose-300 bg-rose-50 text-rose-800";
    }
    
    if (shouldShowCorrect) {
      return (
        baseStyle +
        "border-[var(--emerald)] bg-[color:rgb(2_108_74_/_.08)] text-[var(--emerald)]"
      );
    }
    
    if (isSelected) {
      return baseStyle + "border-primary bg-primary/5 text-primary";
    }
    
    if (isAnswered && !showCorrectAnswer) {
      return baseStyle + "border-[var(--color-border)] bg-[var(--color-muted)] text-grey cursor-not-allowed";
    }
    
    // Subtle verdict tint if enabled
    if (highlightVerdicts && verdict) {
      if (verdict === "correct")
        return (
          baseStyle +
          "border-[var(--emerald)] bg-[color:rgb(2_108_74_/_.06)] text-charcoal hover:border-[var(--emerald)]/80"
        );
      // Treat 'plausible' as neutral – no special tint
      if (verdict === "trap")
        return baseStyle + "border-rose-200 bg-rose-50/40 text-charcoal hover:border-rose-300";
      if (verdict === "incorrect" || verdict === "plausible")
        return baseStyle + "border-[var(--color-border)] bg-white text-charcoal hover:border-gray-300";
    }
    return baseStyle + "border-[var(--color-border)] bg-white text-charcoal hover:border-primary/50 hover:bg-primary/5";
  };

  const getChoiceIcon = (choiceKey: "A" | "B" | "C" | "D") => {
    const isSelected = selectedAnswer === choiceKey;
    const isCorrect = question.correct === choiceKey;
    const isWrong = showCorrectAnswer && isSelected && !isCorrect;
    const shouldShowCorrect = showCorrectAnswer && isCorrect;

    if (isWrong) {
      return <XCircle className="h-5 w-5 text-rose-600 mt-0.5" />;
    }
    
    if (shouldShowCorrect) {
      return <CheckCircle className="h-5 w-5 text-[var(--emerald)] mt-0.5" />;
    }
    
    return (
      <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center text-sm font-semibold mt-0.5 ${
        isSelected 
          ? "border-primary bg-primary text-white" 
          : "border-gray-300 bg-white text-grey"
      }`}>
        {choiceKey}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Question Text */}
      <div className="space-y-4">
        <div className="flex items-start gap-3">
          <div className="bg-primary/10 rounded-lg p-2 mt-1">
            <span className="text-primary font-semibold text-sm">Q</span>
          </div>
          <div className="flex-1">
            <h2 className="text-xl font-semibold text-charcoal leading-relaxed">
              {question.question}
            </h2>
          </div>
        </div>
      </div>

      {/* Answer Choices */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-grey uppercase tracking-wide">
          Select your answer:
        </h3>
        
        <div className="grid gap-3">
          {choices.map((choice) => {
            const ans = analysis?.answers.find((a) => a.key === choice.key);
            const verdictBadgeClass =
              ans?.verdict === "correct"
                ? "bg-[color:rgb(2_108_74_/_.12)] text-[var(--emerald)]"
                : ans?.verdict === "trap"
                ? "bg-rose-100 text-rose-700"
                : "bg-gray-100 text-gray-700";
            const showBadge = ans && ans.verdict !== "plausible";

            return (
              <div
                key={choice.key}
                onClick={() => !isAnswered && onAnswerSelect(choice.key)}
                className={getChoiceStyle(choice.key)}
              >
                {getChoiceIcon(choice.key)}
                <div className="flex-1 space-y-2">
                  <p className="text-base leading-relaxed">{choice.text}</p>

                  {ans && (
                    <div className="mt-1 rounded-md border border-[var(--color-border)] bg-white p-3">
                      {showBadge && (
                        <div className="flex items-center justify-between mb-1">
                          <span className={`px-2 py-0.5 text-xs rounded-full ${verdictBadgeClass}`}>
                            {ans.verdict}
                          </span>
                        </div>
                      )}
                      <div className="text-sm text-charcoal/90">{ans.summary}</div>
                      {ans.bullets?.length ? (
                        <ul className="mt-1 list-disc list-inside text-xs text-grey space-y-0.5">
                          {ans.bullets.map((b, i) => (
                            <li key={i}>{b}</li>
                          ))}
                        </ul>
                      ) : null}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Show correct answer if applicable */}
      {showCorrectAnswer && (
        <div className="pt-4 border-t border-gray-100">
          <div className="text-sm text-right">
            <span className="text-grey">Correct Answer: </span>
            <span className="font-semibold text-green-700">{question.correct}</span>
          </div>
        </div>
      )}
    </div>
  );
}
