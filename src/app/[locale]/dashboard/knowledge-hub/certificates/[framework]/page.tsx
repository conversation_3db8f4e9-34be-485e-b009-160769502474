"use client";

import { useParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { DtcHero } from "@/components/ui/Hero";
import { Dock, DockIcon, DockItem, DockLabel } from "@/components/ui/ai-frontend/DockTabs";
import { useUser } from "@/hooks/useUser";
import ExamSimulator from "@/components/ui/knowledge-hub/Certificates/tabs/ExamSimulator";
import RepairCenter from "@/components/ui/knowledge-hub/Certificates/tabs/RepairCenter";
import LearningHub from "@/components/ui/knowledge-hub/Certificates/tabs/LearningHub";
import Games from "@/components/ui/knowledge-hub/Certificates/tabs/Games";
import Leaderboards from "@/components/ui/knowledge-hub/Certificates/tabs/Leaderboards";
import CertificateConfiguration from "@/components/ui/knowledge-hub/Certificates/tabs/CertificateConfiguration";
import PerformanceManagement from "@/components/ui/knowledge-hub/Certificates/tabs/PerformanceManagement";
import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Settings2, <PERSON><PERSON><PERSON>t, Brain, BarChart3, History } from "lucide-react";

export default function CertificateFrameworkPage() {
  const params = useParams();
  const t = useTranslations("knowledgeHub.certificates");
  const framework = Array.isArray(params?.framework) ? params?.framework[0] : (params?.framework as string);

  const title = (framework?.replace(/-/g, " ") || t("heroTitle")).toUpperCase();

  const { user } = useUser();
  const isAdmin = user?.role === "Admin";
  const [active, setActive] = useState<string>("exam");


  const tabs: Array<{ key: string; label: string; icon: React.ComponentType<React.SVGProps<SVGSVGElement>>; render: () => React.ReactNode; adminOnly?: boolean }> = [
    { key: "exam", label: "Exam Simulator", icon: FileText, render: () => <ExamSimulator /> },
    { key: "repair", label: "Repair Center", icon: Wrench, render: () => <RepairCenter /> },
    { key: "learning", label: "Learning Hub", icon: Brain, render: () => <LearningHub /> },
    { key: "performance", label: "Performance Management", icon: BarChart3, render: () => <PerformanceManagement /> },
    { key: "games", label: "Games", icon: Gamepad2, render: () => <Games /> },
    { key: "leaderboards", label: "Leaderboards", icon: Trophy, render: () => <Leaderboards /> },
    { key: "config", label: "Certificate Configuration", icon: Settings2, render: () => <CertificateConfiguration />, adminOnly: true },
  ];

  const visibleTabs = tabs.filter((t) => !t.adminOnly || isAdmin);

  // Get the active tab label for the hero subtitle
  const activeTab = visibleTabs.find((tab) => tab.key === active);
  const heroSubtitle = activeTab ? `${activeTab.label} • ${t("heroSubtitle")}` : t("heroSubtitle");

  return (
    <div className="space-y-6">
      <DtcHero title={title} subtitle={heroSubtitle} image="hero2">
        {/* Performance Management View History Button */}
        {active === "performance" && (
          <div className="absolute top-6 right-6 pointer-events-auto">
            <button
              onClick={() => (window as any).openPerformanceSidebar?.()}
              className="flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm text-white rounded-lg hover:bg-white/20 transition-colors border border-white/20"
            >
              <History className="h-4 w-4" />
              View History
            </button>
          </div>
        )}

        {/* Dock anchored to bottom of hero */}
        <div className="pointer-events-auto">
          <Dock>
            {visibleTabs.map((tab) => (
              <DockItem key={tab.key}>
                <button
                  type="button"
                  onClick={() => setActive(tab.key)}
                  className={
                    "group relative inline-flex items-center justify-center rounded-2xl px-4 py-4 text-sm font-medium transition " +
                    (active === tab.key
                      ? "bg-white text-gray-800 shadow-sm ring-1 ring-white"
                      : "bg-white/10 text-white hover:bg-white/15")
                  }
                >
                  <DockIcon>
                    {(() => {
                      const Icon = tab.icon;
                      const isActive = active === tab.key;
                      return <Icon className={`h-6 w-6 ${isActive ? "text-gray-900" : "text-white"}`} />;
                    })()}
                  </DockIcon>
                  <DockLabel className="bg-white/95 text-gray-900 ring-1 ring-white">
                    {tab.label}
                  </DockLabel>
                </button>
              </DockItem>
            ))}
          </Dock>
        </div>
      </DtcHero>

      {/* Active content */}
      <div className="w-full">
        {active === "performance" ? (
          <PerformanceManagement onSidebarOpenRequest={() => {}} />
        ) : (
          visibleTabs.find((t) => t.key === active)?.render()
        )}
      </div>
    </div>
  );
}


