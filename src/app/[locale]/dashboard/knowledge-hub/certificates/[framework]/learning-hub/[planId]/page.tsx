"use client";

import { useParams } from "next/navigation";
import { useRouter } from "@/i18n/navigation";
import { useUser } from "@/hooks/useUser";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { ArrowLeft, Clock, Target, CheckCircle, PlayCircle, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { getLearningPlan, updatePlanProgress, type LearningPlan, type LearningTool } from "@/Services/planService";
import { DtcHero } from "@/components/ui/Hero";
import LearningLesson from "@/components/ui/learning-hub/LearningLesson";

export default function PlanPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useUser();
  
  const framework = Array.isArray(params?.framework) ? params?.framework[0] : params?.framework as string;
  const planId = Array.isArray(params?.planId) ? params?.planId[0] : params?.planId as string;
  
  const [plan, setPlan] = useState<LearningPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTool, setActiveTool] = useState<LearningTool | null>(null);

  useEffect(() => {
    async function loadPlan() {
      if (!user?.uid || !planId) return;
      
      try {
        setLoading(true);
        const planData = await getLearningPlan(user.uid, planId);
        if (planData) {
          setPlan(planData);
        } else {
          setError('Learning plan not found');
        }
      } catch (err) {
        console.error('Error loading plan:', err);
        setError('Failed to load learning plan');
      } finally {
        setLoading(false);
      }
    }

    loadPlan();
  }, [user?.uid, planId]);

  const handleToolComplete = async (toolId: string, progress: number, completed: boolean) => {
    if (!user?.uid || !planId) return;
    
    try {
      await updatePlanProgress(user.uid, planId, toolId, progress, completed);
      
      // Refresh plan data
      const updatedPlan = await getLearningPlan(user.uid, planId);
      if (updatedPlan) {
        setPlan(updatedPlan);
      }
    } catch (error) {
      console.error('Error updating progress:', error);
    }
  };

  const handleStartTool = (tool: LearningTool) => {
    setActiveTool(tool);
  };

  const handleBackToOverview = () => {
    setActiveTool(null);
  };

  const getToolIcon = (type: string) => {
    switch (type) {
      case 'flashcards':
        return '🃏';
      case 'trueOrFalse':
        return '✅';
      case 'twoFactsOneLie':
        return '🎯';
      case 'knowledgeGraphs':
        return '🕸️';
      case 'practicalScenarioGuidance':
        return '🎭';
      case 'matchConcepts':
        return '🔗';
      case 'conceptsGuidedLearning':
        return '📚';
      default:
        return '📖';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-[var(--color-muted)] to-white/50">
        <DtcHero
          title="Loading Learning Plan"
          subtitle="Preparing your AI-powered learning experience"
          breadcrumbs={[
            { label: "Knowledge Hub", href: "/dashboard/knowledge-hub" },
            { label: framework, href: `/dashboard/knowledge-hub/certificates/${framework}` },
            { label: "Learning Hub", href: `/dashboard/knowledge-hub/certificates/${framework}/learning-hub` },
            { label: "Plan", href: "#" }
          ]}
        />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-[var(--emerald)] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-[var(--grey)]">Loading your learning plan...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !plan) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-[var(--color-muted)] to-white/50">
        <DtcHero
          title="Plan Not Found"
          subtitle="The learning plan you're looking for doesn't exist"
          breadcrumbs={[
            { label: "Knowledge Hub", href: "/dashboard/knowledge-hub" },
            { label: framework, href: `/dashboard/knowledge-hub/certificates/${framework}` },
            { label: "Learning Hub", href: `/dashboard/knowledge-hub/certificates/${framework}/learning-hub` },
            { label: "Plan", href: "#" }
          ]}
        />
        <div className="max-w-4xl mx-auto px-6 py-12">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button
              onClick={() => router.push(`/dashboard/knowledge-hub/certificates/${framework}/learning-hub`)}
              className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Learning Hub
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // If a tool is active, show the learning lesson component
  if (activeTool) {
    return (
      <LearningLesson
        tool={activeTool}
        plan={plan}
        onComplete={handleToolComplete}
        onBack={handleBackToOverview}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-[var(--color-muted)] to-white/50">
      {/* Hero Section */}
      <DtcHero
        title={plan.title}
        subtitle={plan.description}
        breadcrumbs={[
          { label: "Knowledge Hub", href: "/dashboard/knowledge-hub" },
          { label: framework, href: `/dashboard/knowledge-hub/certificates/${framework}` },
          { label: "Learning Hub", href: `/dashboard/knowledge-hub/certificates/${framework}/learning-hub` },
          { label: plan.title, href: "#" }
        ]}
      />

      <div className="max-w-7xl mx-auto px-6 py-12">
        {/* Plan Overview */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <Button
              variant="outline"
              onClick={() => router.push(`/dashboard/knowledge-hub/certificates/${framework}/learning-hub`)}
              className="text-[var(--charcoal)] border-[var(--color-border)]"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Learning Hub
            </Button>
            
            <div className="flex items-center gap-4 text-sm text-[var(--grey)]">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {plan.estimatedDuration}
              </div>
              <div className="flex items-center gap-1">
                <Target className="w-4 h-4" />
                {plan.difficulty}
              </div>
              <div className="flex items-center gap-1">
                <BookOpen className="w-4 h-4" />
                {plan.tools.length} Tools
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-[var(--color-border)] mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-[var(--charcoal)]">Learning Progress</h3>
              <span className="text-2xl font-bold text-[var(--emerald)]">{plan.progressPercentage}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-gradient-to-r from-[var(--emerald)] to-[var(--bright-green)] h-3 rounded-full transition-all duration-500"
                style={{ width: `${plan.progressPercentage}%` }}
              />
            </div>
            <div className="flex justify-between text-sm text-[var(--grey)] mt-2">
              <span>{plan.tools.filter(t => t.completed).length} of {plan.tools.length} tools completed</span>
              <span>{plan.status === 'completed' ? 'Completed!' : 'In Progress'}</span>
            </div>
          </div>
        </div>

        {/* Learning Tools Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {plan.tools.map((tool, index) => (
            <motion.div
              key={tool.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={cn(
                "bg-white rounded-2xl p-6 shadow-sm border transition-all duration-200 cursor-pointer",
                tool.completed 
                  ? "border-[var(--emerald)] bg-gradient-to-br from-[var(--emerald)]/5 to-[var(--emerald)]/10" 
                  : "border-[var(--color-border)] hover:shadow-lg hover:border-[var(--emerald)]/50"
              )}
              onClick={() => handleStartTool(tool)}
            >
              {/* Tool Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="text-2xl">{getToolIcon(tool.type)}</div>
                  <div>
                    <h3 className="font-semibold text-[var(--charcoal)] mb-1">{tool.name}</h3>
                    <p className="text-sm text-[var(--grey)]">{tool.estimatedTime}</p>
                  </div>
                </div>
                {tool.completed && (
                  <CheckCircle className="w-6 h-6 text-[var(--emerald)]" />
                )}
              </div>

              {/* Tool Description */}
              <p className="text-sm text-[var(--grey)] mb-4 line-clamp-2">
                {tool.description}
              </p>

              {/* Progress Bar */}
              {tool.progress !== undefined && tool.progress > 0 && (
                <div className="mb-4">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-[var(--emerald)] h-2 rounded-full transition-all duration-300"
                      style={{ width: `${tool.progress}%` }}
                    />
                  </div>
                </div>
              )}

              {/* Action Button */}
              <Button
                variant="outline"
                className="w-full justify-center group"
                onClick={(e) => {
                  e.stopPropagation();
                  handleStartTool(tool);
                }}
              >
                {tool.completed ? (
                  <>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Review
                  </>
                ) : tool.progress && tool.progress > 0 ? (
                  <>
                    <PlayCircle className="w-4 h-4 mr-2" />
                    Continue
                  </>
                ) : (
                  <>
                    <PlayCircle className="w-4 h-4 mr-2" />
                    Start
                  </>
                )}
              </Button>
            </motion.div>
          ))}
        </div>

        {/* Learning Objectives */}
        {plan.learningObjectives.length > 0 && (
          <div className="mt-12 bg-white rounded-2xl p-6 shadow-sm border border-[var(--color-border)]">
            <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-4">Learning Objectives</h3>
            <ul className="space-y-2">
              {plan.learningObjectives.map((objective, index) => (
                <li key={index} className="flex items-start gap-2 text-[var(--grey)]">
                  <Target className="w-4 h-4 mt-0.5 text-[var(--emerald)] flex-shrink-0" />
                  {objective}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
