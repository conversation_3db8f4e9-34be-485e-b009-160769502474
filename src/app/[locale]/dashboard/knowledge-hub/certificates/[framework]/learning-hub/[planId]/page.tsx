"use client";

import { usePara<PERSON>, useRouter } from "next/navigation";
import { useUser } from "@/hooks/useUser";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { ArrowLeft, Clock, Target, BookOpen, CheckCircle2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { getLearningPlan, type LearningPlan } from "@/Services/planService";
import LearningLesson from "@/components/ui/learning-hub/LearningLesson";
import { DtcHero } from "@/components/ui/Hero";

export default function LearningPlanPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useUser();
  
  const planId = Array.isArray(params?.planId) ? params?.planId[0] : params?.planId as string;
  const framework = Array.isArray(params?.framework) ? params?.framework[0] : params?.framework as string;
  
  const [plan, setPlan] = useState<LearningPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState<'overview' | 'lesson'>('overview');

  useEffect(() => {
    async function loadPlan() {
      if (!user?.uid || !planId) return;
      
      try {
        setLoading(true);
        const planData = await getLearningPlan(user.uid, planId);
        if (!planData) {
          setError('Learning plan not found');
          return;
        }
        setPlan(planData);
      } catch (err) {
        console.error('Error loading plan:', err);
        setError('Failed to load learning plan');
      } finally {
        setLoading(false);
      }
    }

    loadPlan();
  }, [user?.uid, planId]);

  const handleStartLearning = () => {
    setCurrentView('lesson');
  };

  const handleBackToOverview = () => {
    setCurrentView('overview');
  };

  const handleBackToCertificate = () => {
    router.push(`/dashboard/knowledge-hub/certificates/${framework}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-[var(--color-muted)] to-white/50">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-[var(--emerald)] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-[var(--grey)]">Loading your learning plan...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !plan) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-[var(--color-muted)] to-white/50">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <BookOpen className="w-8 h-8 text-red-600" />
            </div>
            <h2 className="text-xl font-semibold text-[var(--charcoal)] mb-2">Plan Not Found</h2>
            <p className="text-[var(--grey)] mb-6">{error || 'The learning plan you requested could not be found.'}</p>
            <Button onClick={handleBackToCertificate} variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Certificate
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (currentView === 'lesson') {
    return (
      <LearningLesson 
        plan={plan} 
        onBack={handleBackToOverview}
        onPlanUpdate={setPlan}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-[var(--color-muted)] to-white/50">
      {/* Hero Section */}
      <DtcHero
        title={plan.title}
        subtitle={plan.description}
        breadcrumbs={[
          { label: "Knowledge Hub", href: "/dashboard/knowledge-hub" },
          { label: framework, href: `/dashboard/knowledge-hub/certificates/${framework}` },
          { label: "Learning Hub", href: `/dashboard/knowledge-hub/certificates/${framework}` },
          { label: plan.title, href: "#" }
        ]}
      />

      <div className="max-w-7xl mx-auto px-6 py-12">
        {/* Back Button */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="mb-8"
        >
          <Button 
            onClick={handleBackToCertificate}
            variant="ghost" 
            className="text-[var(--grey)] hover:text-[var(--charcoal)]"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to {framework}
          </Button>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Plan Overview */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-2xl p-8 shadow-sm border border-[var(--color-border)]"
            >
              <div className="flex items-start justify-between mb-6">
                <div>
                  <h1 className="text-3xl font-bold text-[var(--charcoal)] mb-2">{plan.title}</h1>
                  <p className="text-[var(--grey)] text-lg">{plan.description}</p>
                </div>
                <div className="flex items-center gap-2 px-3 py-1 bg-[var(--emerald)]/10 text-[var(--emerald)] rounded-full text-sm font-medium">
                  <Target className="w-4 h-4" />
                  {plan.difficulty}
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-[var(--charcoal)]">Progress</span>
                  <span className="text-sm text-[var(--grey)]">{plan.progressPercentage}% Complete</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <motion.div
                    className="bg-[var(--emerald)] h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${plan.progressPercentage}%` }}
                    transition={{ duration: 1, ease: "easeOut" }}
                  />
                </div>
              </div>

              {/* Learning Objectives */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-4">Learning Objectives</h3>
                <ul className="space-y-2">
                  {plan.learningObjectives.map((objective, index) => (
                    <motion.li
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-start gap-3"
                    >
                      <CheckCircle2 className="w-5 h-5 text-[var(--emerald)] mt-0.5 flex-shrink-0" />
                      <span className="text-[var(--charcoal)]">{objective}</span>
                    </motion.li>
                  ))}
                </ul>
              </div>

              {/* Start Learning Button */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Button
                  onClick={handleStartLearning}
                  className="w-full bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white py-4 text-lg font-semibold"
                  size="lg"
                >
                  {plan.progressPercentage > 0 ? 'Continue Learning' : 'Start Learning'}
                </Button>
              </motion.div>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Plan Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-2xl p-6 shadow-sm border border-[var(--color-border)]"
            >
              <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-4">Plan Details</h3>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Clock className="w-5 h-5 text-[var(--grey)]" />
                  <div>
                    <p className="text-sm text-[var(--grey)]">Estimated Duration</p>
                    <p className="font-medium text-[var(--charcoal)]">{plan.estimatedDuration}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <BookOpen className="w-5 h-5 text-[var(--grey)]" />
                  <div>
                    <p className="text-sm text-[var(--grey)]">Learning Tools</p>
                    <p className="font-medium text-[var(--charcoal)]">{plan.tools.length} Interactive Tools</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Target className="w-5 h-5 text-[var(--grey)]" />
                  <div>
                    <p className="text-sm text-[var(--grey)]">Difficulty</p>
                    <p className="font-medium text-[var(--charcoal)]">{plan.difficulty}</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Tools Preview */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-2xl p-6 shadow-sm border border-[var(--color-border)]"
            >
              <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-4">Learning Tools</h3>
              <div className="space-y-3">
                {plan.tools.slice(0, 5).map((tool, index) => (
                  <div key={tool.id} className="flex items-center gap-3">
                    <div className={cn(
                      "w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium",
                      tool.completed 
                        ? "bg-[var(--emerald)] text-white" 
                        : index === plan.currentToolIndex
                        ? "bg-[var(--bright-green)] text-white"
                        : "bg-gray-200 text-[var(--grey)]"
                    )}>
                      {tool.completed ? '✓' : index + 1}
                    </div>
                    <div className="flex-1">
                      <p className={cn(
                        "text-sm font-medium",
                        tool.completed ? "text-[var(--emerald)]" : "text-[var(--charcoal)]"
                      )}>
                        {tool.name}
                      </p>
                      <p className="text-xs text-[var(--grey)]">{tool.estimatedTime}</p>
                    </div>
                  </div>
                ))}
                {plan.tools.length > 5 && (
                  <p className="text-xs text-[var(--grey)] text-center pt-2">
                    +{plan.tools.length - 5} more tools
                  </p>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
