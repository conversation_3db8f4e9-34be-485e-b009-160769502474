"use client";

import { useParams } from "next/navigation";
import { useRouter } from "@/i18n/navigation";
import { useUser } from "@/hooks/useUser";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Plus, BookOpen, Clock, Target, TrendingUp, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { getUserLearningPlans, getPlansByCertificate, type LearningPlan } from "@/Services/planService";
import { DtcHero } from "@/components/ui/Hero";

export default function LearningHubPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useUser();
  
  const framework = Array.isArray(params?.framework) ? params?.framework[0] : params?.framework as string;
  
  const [plans, setPlans] = useState<LearningPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadPlans() {
      if (!user?.uid || !framework) return;
      
      try {
        setLoading(true);
        const userPlans = await getPlansByCertificate(user.uid, framework);
        setPlans(userPlans);
      } catch (err) {
        console.error('Error loading plans:', err);
        setError('Failed to load learning plans');
      } finally {
        setLoading(false);
      }
    }

    loadPlans();
  }, [user?.uid, framework]);

  const handleCreateNewPlan = () => {
    router.push(`/dashboard/knowledge-hub/certificates/${framework}`);
  };

  const handleViewPlan = (planId: string) => {
    router.push(`/dashboard/knowledge-hub/certificates/${framework}/learning-hub/${planId}`);
  };

  const getStatusColor = (status: LearningPlan['status']) => {
    switch (status) {
      case 'active':
        return 'text-[var(--emerald)] bg-[var(--emerald)]/10';
      case 'completed':
        return 'text-[var(--bright-green)] bg-[var(--bright-green)]/10';
      case 'paused':
        return 'text-yellow-600 bg-yellow-100';
      case 'draft':
        return 'text-[var(--grey)] bg-gray-100';
      default:
        return 'text-[var(--grey)] bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-[var(--color-muted)] to-white/50">
        <DtcHero
          title="Learning Hub"
          subtitle="AI-powered personalized learning plans"
          breadcrumbs={[
            { label: "Knowledge Hub", href: "/dashboard/knowledge-hub" },
            { label: framework, href: `/dashboard/knowledge-hub/certificates/${framework}` },
            { label: "Learning Hub", href: "#" }
          ]}
        />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-[var(--emerald)] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-[var(--grey)]">Loading your learning plans...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-[var(--color-muted)] to-white/50">
      {/* Hero Section */}
      <DtcHero
        title="Learning Hub"
        subtitle="AI-powered personalized learning plans for your certification journey"
        breadcrumbs={[
          { label: "Knowledge Hub", href: "/dashboard/knowledge-hub" },
          { label: framework, href: `/dashboard/knowledge-hub/certificates/${framework}` },
          { label: "Learning Hub", href: "#" }
        ]}
      />

      <div className="max-w-7xl mx-auto px-6 py-12">
        {/* Header Actions */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-2xl font-bold text-[var(--charcoal)]">Your Learning Plans</h2>
            <p className="text-[var(--grey)] mt-1">
              {plans.length === 0 
                ? "Create your first AI-powered learning plan" 
                : `${plans.length} learning plan${plans.length === 1 ? '' : 's'} for ${framework}`
              }
            </p>
          </div>
          <Button
            onClick={handleCreateNewPlan}
            className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create New Plan
          </Button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Plans Grid */}
        {plans.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-20"
          >
            <div className="w-24 h-24 bg-[var(--emerald)]/10 rounded-full flex items-center justify-center mx-auto mb-6">
              <BookOpen className="w-12 h-12 text-[var(--emerald)]" />
            </div>
            <h3 className="text-xl font-semibold text-[var(--charcoal)] mb-2">No Learning Plans Yet</h3>
            <p className="text-[var(--grey)] mb-8 max-w-md mx-auto">
              Create your first AI-powered learning plan to start your personalized learning journey for {framework}.
            </p>
            <Button
              onClick={handleCreateNewPlan}
              className="bg-[var(--emerald)] hover:bg-[var(--emerald-deep)] text-white"
              size="lg"
            >
              <Plus className="w-5 h-5 mr-2" />
              Create Your First Plan
            </Button>
          </motion.div>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {plans.map((plan, index) => (
              <motion.div
                key={plan.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-sm border border-[var(--color-border)] hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => handleViewPlan(plan.id)}
              >
                {/* Plan Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="font-semibold text-[var(--charcoal)] mb-1 line-clamp-2">
                      {plan.title}
                    </h3>
                    <p className="text-sm text-[var(--grey)] line-clamp-2">
                      {plan.description}
                    </p>
                  </div>
                  <div className={cn(
                    "px-2 py-1 rounded-full text-xs font-medium ml-3 flex-shrink-0",
                    getStatusColor(plan.status)
                  )}>
                    {plan.status}
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-[var(--grey)]">Progress</span>
                    <span className="text-xs font-medium text-[var(--charcoal)]">
                      {plan.progressPercentage}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div
                      className="bg-[var(--emerald)] h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${plan.progressPercentage}%` }}
                    />
                  </div>
                </div>

                {/* Plan Stats */}
                <div className="grid grid-cols-3 gap-3 mb-4">
                  <div className="text-center">
                    <div className="text-sm font-semibold text-[var(--charcoal)]">
                      {plan.tools.length}
                    </div>
                    <div className="text-xs text-[var(--grey)]">Tools</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm font-semibold text-[var(--charcoal)]">
                      {plan.completedTools}
                    </div>
                    <div className="text-xs text-[var(--grey)]">Complete</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm font-semibold text-[var(--charcoal)]">
                      {plan.difficulty}
                    </div>
                    <div className="text-xs text-[var(--grey)]">Level</div>
                  </div>
                </div>

                {/* Plan Meta */}
                <div className="flex items-center justify-between text-xs text-[var(--grey)] mb-4">
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {plan.estimatedDuration}
                  </div>
                  <div className="flex items-center gap-1">
                    <Target className="w-3 h-3" />
                    {plan.intensity}
                  </div>
                </div>

                {/* Action Button */}
                <Button
                  variant="outline"
                  className="w-full justify-between group"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleViewPlan(plan.id);
                  }}
                >
                  <span>
                    {plan.progressPercentage === 0 
                      ? 'Start Learning' 
                      : plan.progressPercentage === 100 
                      ? 'Review Plan' 
                      : 'Continue Learning'
                    }
                  </span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </motion.div>
            ))}
          </div>
        )}

        {/* Quick Stats */}
        {plans.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mt-12 grid md:grid-cols-4 gap-6"
          >
            <div className="bg-white rounded-xl p-6 text-center border border-[var(--color-border)]">
              <div className="text-2xl font-bold text-[var(--emerald)] mb-1">
                {plans.length}
              </div>
              <div className="text-sm text-[var(--grey)]">Total Plans</div>
            </div>
            <div className="bg-white rounded-xl p-6 text-center border border-[var(--color-border)]">
              <div className="text-2xl font-bold text-[var(--bright-green)] mb-1">
                {plans.filter(p => p.status === 'completed').length}
              </div>
              <div className="text-sm text-[var(--grey)]">Completed</div>
            </div>
            <div className="bg-white rounded-xl p-6 text-center border border-[var(--color-border)]">
              <div className="text-2xl font-bold text-[var(--lime-green)] mb-1">
                {plans.filter(p => p.status === 'active').length}
              </div>
              <div className="text-sm text-[var(--grey)]">Active</div>
            </div>
            <div className="bg-white rounded-xl p-6 text-center border border-[var(--color-border)]">
              <div className="text-2xl font-bold text-[var(--charcoal)] mb-1">
                {Math.round(plans.reduce((acc, p) => acc + p.progressPercentage, 0) / plans.length) || 0}%
              </div>
              <div className="text-sm text-[var(--grey)]">Avg Progress</div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
