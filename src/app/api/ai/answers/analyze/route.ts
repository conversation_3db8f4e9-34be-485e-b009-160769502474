import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";
import crypto from "crypto";
import type { QuestionExplanation } from "@/Services/questionsService";
import { getFirebaseAdmin } from "@/app/Firebase/admin";

const schema = z.object({
  answers: z.array(
    z.object({
      key: z.enum(["A", "B", "C", "D"]),
      verdict: z.enum(["correct", "plausible", "trap", "incorrect"]),
      summary: z.string().min(1).max(500),
      bullets: z.array(z.string().min(1)).max(3),
    })
  ),
  // Allow a bit more and we'll clamp after generation to avoid validation failures
  knowledgeAreas: z.array(z.string()).min(0).max(10),
  similarQuestionThemes: z.array(z.string()).max(10),
});

type Body = {
  question: string;
  choices: [string, string, string, string];
  correctKey: "A" | "B" | "C" | "D";
  userAnswerKey?: "A" | "B" | "C" | "D" | null;
};

function stableHash(input: unknown): string {
  const json = JSON.stringify(input);
  return crypto.createHash("sha256").update(json).digest("hex");
}

async function readSharedExplanation(hash: string): Promise<QuestionExplanation | null> {
  try {
    const { db } = await getFirebaseAdmin();
    const snap = await db.doc(`questions/${hash}`).get();
    const data = snap.exists ? (snap.data() as any) : null;
    return data?.explanation ?? null;
  } catch {
    // If admin is not configured, skip cache
    return null;
  }
}

async function writeSharedExplanation(
  hash: string,
  payload: {
    explanation: QuestionExplanation;
    question: string;
    choices: [string, string, string, string];
    correct: "A" | "B" | "C" | "D";
    model?: string;
  }
) {
  try {
    const { db } = await getFirebaseAdmin();
    await db.doc(`questions/${hash}`).set(
      { ...payload, lastComputedAt: new Date() },
      { merge: true }
    );
  } catch {
    // Ignore write errors when admin isn't configured
  }
}

export async function POST(req: Request) {
  try {
    const body = (await req.json()) as Body;
    const { question, choices, correctKey } = body;

    if (!question || !choices || !correctKey) {
      return Response.json({ error: "Missing required fields" }, { status: 400 });
    }

    const hash = stableHash({ q: question, c: choices, k: correctKey });

    // Check shared cache first (server-side via Admin SDK)
    const cached = await readSharedExplanation(hash);
    if (cached) {
      return Response.json({ explanation: cached, cached: true });
    }

    const { object } = await generateObject({
      model: google("gemini-2.5-pro"),
      maxTokens: 5000,
      schema,
      prompt: [
        "You are a senior exam coach. Analyze the multiple-choice question.",
        "For each option (A-D), explain briefly why it is correct or incorrect.",
        "Mark incorrect but tempting options as 'plausible' or 'trap' as appropriate.",
        "Provide up to 3 short bullet reasons per option.",
        "Also list concise knowledge areas (max 5) and similar themes (max 3).",
        "Return only structured data per schema.",
        "\nQuestion:",
        question,
        "\nChoices:",
        `A) ${choices[0]}`,
        `B) ${choices[1]}`,
        `C) ${choices[2]}`,
        `D) ${choices[3]}`,
        `\nCorrect: ${correctKey}`,
      ].join("\n"),
      temperature: 0.2,
    });

    const explanation: QuestionExplanation = {
      answers: object.answers,
      knowledgeAreas: (object.knowledgeAreas ?? []).slice(0, 5),
      similarQuestionThemes: (object.similarQuestionThemes ?? []).slice(0, 3),
      model: "gemini-2.5-pro",
      updatedAt: new Date(),
    };

    await writeSharedExplanation(hash, {
      explanation,
      question,
      choices,
      correct: correctKey,
      model: "gemini-2.5-pro",
    });

    return Response.json({ explanation, cached: false });
  } catch (err) {
    console.error("/api/ai/answers/analyze error", err);
    return Response.json({ error: "Failed to analyze answers" }, { status: 500 });
  }
}


