import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";

// Learning Plan Schema
const LearningPlanSchema = z.object({
  topic: z.string().min(1),
  intensity: z.enum(["detailed", "general", "simple"]),
  focusAreas: z.array(z.string()).optional(),
  certificateContext: z.object({
    id: z.string(),
    name: z.string(),
    provider: z.string(),
    description: z.string(),
  }).optional(),
});

// Tool Content Schemas
const FlashcardContentSchema = z.object({
  flashcards: z.array(z.object({
    id: z.string(),
    front: z.string(),
    back: z.string(),
    difficulty: z.enum(["easy", "medium", "hard"]).optional(),
    category: z.string().optional(),
    tags: z.array(z.string()).optional(),
    examples: z.array(z.string()).optional(),
    relatedConcepts: z.array(z.string()).optional(),
  }))
});

const TrueFalseContentSchema = z.object({
  questions: z.array(z.object({
    id: z.string(),
    statement: z.string(),
    correct: z.boolean(),
    explanation: z.string(),
    category: z.string().optional(),
    difficulty: z.enum(["easy", "medium", "hard"]).optional(),
    commonMisconception: z.string().optional(),
    realWorldExample: z.string().optional(),
    relatedConcepts: z.array(z.string()).optional(),
  }))
});

const TwoFactsOneLieContentSchema = z.object({
  questions: z.array(z.object({
    id: z.string(),
    topic: z.string(),
    statements: z.tuple([z.string(), z.string(), z.string()]),
    lieIndex: z.union([z.literal(0), z.literal(1), z.literal(2)]),
    explanations: z.tuple([z.string(), z.string(), z.string()]),
    difficulty: z.enum(["easy", "medium", "hard"]).optional(),
    category: z.string().optional(),
    learningPoints: z.array(z.string()).optional(),
  }))
});

const ConceptsGuidedLearningContentSchema = z.object({
  learningPath: z.object({
    id: z.string(),
    title: z.string(),
    description: z.string(),
    difficulty: z.enum(["beginner", "intermediate", "advanced"]),
    estimatedTime: z.string(),
    steps: z.array(z.object({
      id: z.string(),
      title: z.string(),
      content: z.string(),
      type: z.enum(["introduction", "explanation", "example", "practice", "summary"]),
      keyPoints: z.array(z.string()).optional(),
      examples: z.array(z.string()).optional(),
      practiceQuestion: z.object({
        question: z.string(),
        options: z.array(z.string()),
        correctIndex: z.number(),
        explanation: z.string(),
      }).optional(),
      tips: z.array(z.string()).optional(),
      commonMistakes: z.array(z.string()).optional(),
      furtherReading: z.array(z.string()).optional(),
    })),
    learningObjectives: z.array(z.string()),
  })
});

const MatchConceptsContentSchema = z.object({
  pairs: z.array(z.object({
    id: z.string(),
    concept: z.string(),
    definition: z.string(),
    category: z.string().optional(),
    difficulty: z.enum(["easy", "medium", "hard"]).optional(),
    examples: z.array(z.string()).optional(),
    commonConfusions: z.array(z.string()).optional(),
  }))
});

const KnowledgeGraphsContentSchema = z.object({
  nodes: z.array(z.object({
    id: z.string(),
    label: z.string(),
    type: z.enum(["concept", "principle", "application", "process", "tool"]),
    description: z.string(),
    properties: z.object({
      importance: z.enum(["high", "medium", "low"]).optional(),
      complexity: z.enum(["basic", "intermediate", "advanced"]).optional(),
      category: z.string().optional(),
    }).optional(),
  })),
  edges: z.array(z.object({
    id: z.string(),
    source: z.string(),
    target: z.string(),
    relationship: z.enum(["depends on", "implements", "relates to", "causes", "enables", "requires"]),
    description: z.string().optional(),
    strength: z.enum(["strong", "medium", "weak"]).optional(),
  }))
});

const PracticalScenarioGuidanceContentSchema = z.object({
  scenarios: z.array(z.object({
    id: z.string(),
    title: z.string(),
    description: z.string(),
    context: z.string(),
    challenge: z.string(),
    guidanceSteps: z.array(z.object({
      step: z.number(),
      action: z.string(),
      reasoning: z.string(),
      expectedOutcome: z.string().optional(),
      commonPitfalls: z.string().optional(),
    })),
    outcome: z.string(),
    difficulty: z.enum(["easy", "medium", "hard"]).optional(),
    realWorldApplication: z.string().optional(),
    variations: z.array(z.string()).optional(),
  }))
});

// Plan Generation Response Schema
const PlanResponseSchema = z.object({
  planId: z.string(),
  title: z.string(),
  description: z.string(),
  estimatedDuration: z.string(),
  difficulty: z.enum(["Beginner", "Intermediate", "Advanced"]),
  tools: z.array(z.object({
    id: z.string(),
    name: z.string(),
    type: z.enum([
      "flashcards",
      "trueOrFalse",
      "twoFactsOneLie",
      "knowledgeGraphs",
      "practicalScenarioGuidance",
      "matchConcepts",
      "conceptsGuidedLearning"
    ]),
    description: z.string(),
    order: z.number(),
    estimatedTime: z.string(),
    content: z.union([
      FlashcardContentSchema,
      TrueFalseContentSchema,
      TwoFactsOneLieContentSchema,
      ConceptsGuidedLearningContentSchema,
      MatchConceptsContentSchema,
      KnowledgeGraphsContentSchema,
      PracticalScenarioGuidanceContentSchema,
      z.record(z.string(), z.any()), // For other tool types
    ]),
  })),
  learningObjectives: z.array(z.string()),
  prerequisites: z.array(z.string()).optional(),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const parsed = LearningPlanSchema.safeParse(body);

    if (!parsed.success) {
      return Response.json({
        error: "Invalid payload",
        details: parsed.error.issues
      }, { status: 400 });
    }

    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return Response.json({
        error: "Missing GOOGLE_GENERATIVE_AI_API_KEY"
      }, { status: 500 });
    }

    const { topic, intensity, focusAreas, certificateContext } = parsed.data;

    // Generate unique plan ID
    const planId = `plan_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Build context-aware prompt
    const contextPrompt = buildLearningPlanPrompt({
      topic,
      intensity,
      focusAreas,
      certificateContext,
      planId
    });

    const result = await generateObject({
      model: google("gemini-2.5-pro"),
      maxTokens: 8000,
      schema: PlanResponseSchema,
      prompt: contextPrompt,
    });

    return Response.json({
      plan: result.object,
      usage: result.usage
    });

  } catch (e: any) {
    console.error("Learning Hub API Error:", e);
    return Response.json({
      error: e?.message ?? "Server error"
    }, { status: 500 });
  }
}

function buildLearningPlanPrompt(args: {
  topic: string;
  intensity: "detailed" | "general" | "simple";
  focusAreas?: string[];
  certificateContext?: any;
  planId: string;
}) {
  const { topic, intensity, focusAreas, certificateContext, planId } = args;

  const intensityMap = {
    simple: "beginner-friendly with basic concepts",
    general: "comprehensive coverage with moderate depth",
    detailed: "in-depth analysis with advanced concepts"
  };

  const focusAreasText = focusAreas?.length
    ? `Focus specifically on: ${focusAreas.join(", ")}.`
    : "";

  const certificateText = certificateContext
    ? `This learning plan is for the ${certificateContext.name} certification by ${certificateContext.provider}. Context: ${certificateContext.description}`
    : "";

  return `You are a world-class learning designer and subject matter expert creating the most comprehensive, detailed, and engaging learning plans possible.

Create an EXTREMELY DETAILED and COMPREHENSIVE learning plan for the topic: "${topic}"
Intensity level: ${intensityMap[intensity]}
${focusAreasText}
${certificateText}

CRITICAL REQUIREMENTS FOR MAXIMUM DETAIL:
- Each tool must contain extensive, in-depth content
- Provide comprehensive explanations, not brief summaries
- Include real-world examples, case studies, and practical applications
- Create detailed step-by-step guidance
- Add multiple difficulty levels within each tool
- Include advanced concepts and nuanced understanding
- Provide extensive practice opportunities
- Add detailed explanations for every answer and concept

IMPORTANT: You must provide complete, detailed content for each tool. Here are the exact content structures required:

**For Flashcards tools:**
content: {
  flashcards: [
    {
      id: "1",
      front: "Detailed question with context and examples",
      back: "Comprehensive answer with explanations, examples, real-world applications, and additional context. Include why this matters and how it connects to other concepts.",
      difficulty: "easy|medium|hard",
      category: "Specific topic category",
      tags: ["tag1", "tag2"],
      examples: ["Real example 1", "Real example 2"],
      relatedConcepts: ["Related concept 1", "Related concept 2"]
    }
  ]
}
MINIMUM: Create 15-25 flashcards per tool with varying difficulty levels

**For True/False tools:**
content: {
  questions: [
    {
      id: "1",
      statement: "Detailed, nuanced statement that requires deep understanding to evaluate correctly",
      correct: true|false,
      explanation: "Comprehensive explanation covering why this is true/false, including context, exceptions, real-world implications, and common misconceptions. Provide examples and counter-examples.",
      category: "Specific topic category",
      difficulty: "easy|medium|hard",
      commonMisconception: "What people often get wrong about this",
      realWorldExample: "Concrete example from practice",
      relatedConcepts: ["Concept 1", "Concept 2"]
    }
  ]
}
MINIMUM: Create 20-30 true/false questions with detailed explanations

**For Two Facts One Lie tools:**
content: {
  questions: [
    {
      id: "1",
      topic: "Specific detailed topic name",
      statements: [
        "Detailed fact 1 with specific information and context",
        "Detailed fact 2 with specific information and context",
        "Convincing lie that sounds plausible but is incorrect"
      ],
      lieIndex: 2,
      explanations: [
        "Comprehensive explanation of why fact 1 is true, including evidence, examples, and real-world applications",
        "Comprehensive explanation of why fact 2 is true, including evidence, examples, and real-world applications",
        "Detailed explanation of why this is false, including what makes it seem plausible, common misconceptions, and the correct information"
      ],
      difficulty: "easy|medium|hard",
      category: "Specific category",
      learningPoints: ["Key learning point 1", "Key learning point 2"]
    }
  ]
}
MINIMUM: Create 15-20 two facts one lie questions with sophisticated content

**For Concepts Guided Learning tools:**
content: {
  learningPath: {
    id: "path1",
    title: "Comprehensive learning path title",
    description: "Detailed description explaining what learners will master and why it matters",
    difficulty: "beginner|intermediate|advanced",
    estimatedTime: "X minutes",
    steps: [
      {
        id: "step1",
        title: "Detailed step title that clearly indicates what will be learned",
        content: "EXTREMELY DETAILED explanation (minimum 300-500 words) covering the concept thoroughly with multiple perspectives, historical context, current applications, future implications, and deep technical details. Include analogies, metaphors, and multiple examples.",
        type: "introduction|explanation|example|practice|summary",
        keyPoints: [
          "Detailed key point 1 with full explanation",
          "Detailed key point 2 with full explanation",
          "Detailed key point 3 with full explanation",
          "Detailed key point 4 with full explanation"
        ],
        examples: [
          "Comprehensive real-world example 1 with full context and explanation",
          "Comprehensive real-world example 2 with full context and explanation",
          "Comprehensive real-world example 3 with full context and explanation"
        ],
        practiceQuestion: {
          question: "Detailed, thought-provoking practice question that tests deep understanding",
          options: [
            "Detailed option A with specific context",
            "Detailed option B with specific context",
            "Detailed option C with specific context",
            "Detailed option D with specific context"
          ],
          correctIndex: 0,
          explanation: "Comprehensive explanation of why this is correct, why other options are wrong, and what this teaches us about the broader concept"
        },
        tips: [
          "Practical tip 1 for mastering this concept",
          "Practical tip 2 for avoiding common mistakes",
          "Practical tip 3 for real-world application"
        ],
        commonMistakes: [
          "Common mistake 1 and how to avoid it",
          "Common mistake 2 and how to avoid it"
        ],
        furtherReading: [
          "Additional resource 1 for deeper learning",
          "Additional resource 2 for practical application"
        ]
      }
    ],
    learningObjectives: [
      "Detailed objective 1 with specific, measurable outcomes",
      "Detailed objective 2 with specific, measurable outcomes",
      "Detailed objective 3 with specific, measurable outcomes"
    ]
  }
}
MINIMUM: Create 8-15 comprehensive steps with extensive content for each step

**For Match Concepts tools:**
content: {
  pairs: [
    {
      id: "1",
      concept: "Detailed term or concept with context",
      definition: "Comprehensive definition with examples, applications, and nuanced explanations. Include why this matters and how it relates to other concepts.",
      category: "Specific category",
      difficulty: "easy|medium|hard",
      examples: ["Real example 1", "Real example 2"],
      commonConfusions: ["What this is often confused with and why"]
    }
  ]
}
MINIMUM: Create 20-30 concept pairs with detailed definitions

**For Knowledge Graphs tools:**
content: {
  nodes: [
    {
      id: "1",
      label: "Detailed concept name",
      type: "concept|principle|application|process|tool",
      description: "Comprehensive description explaining the concept, its importance, applications, and relationships to other concepts",
      properties: {
        importance: "high|medium|low",
        complexity: "basic|intermediate|advanced",
        category: "Specific category"
      }
    }
  ],
  edges: [
    {
      id: "1",
      source: "1",
      target: "2",
      relationship: "depends on|implements|relates to|causes|enables|requires",
      description: "Detailed explanation of how these concepts relate",
      strength: "strong|medium|weak"
    }
  ]
}
MINIMUM: Create 15-25 nodes and 20-35 edges for comprehensive knowledge mapping

**For Practical Scenario Guidance tools:**
content: {
  scenarios: [
    {
      id: "1",
      title: "Detailed, realistic scenario title",
      description: "Comprehensive scenario description with full context, stakeholders, constraints, and background information",
      context: "Extensive background context including industry setting, organizational context, technical environment, and relevant constraints",
      challenge: "Detailed description of what needs to be solved, including complexity factors, potential risks, and success criteria",
      guidanceSteps: [
        {
          step: 1,
          action: "Detailed action to take with specific instructions and considerations",
          reasoning: "Comprehensive explanation of why this action is recommended, including alternatives considered and potential risks",
          expectedOutcome: "What should happen as a result",
          commonPitfalls: "What could go wrong and how to avoid it"
        }
      ],
      outcome: "Detailed expected result with success metrics and follow-up actions",
      difficulty: "easy|medium|hard",
      realWorldApplication: "How this applies in actual practice",
      variations: ["Alternative scenario 1", "Alternative scenario 2"]
    }
  ]
}
MINIMUM: Create 8-12 comprehensive scenarios with detailed guidance

CRITICAL REQUIREMENTS FOR MAXIMUM DETAIL:
- Plan ID: ${planId}
- Create 6-8 learning tools in logical sequence for comprehensive coverage
- Each tool MUST have complete, extensive content as shown above with MINIMUM quantities specified
- Start with foundational tools (guided learning with 8-15 steps, flashcards with 15-25 cards)
- Progress to application tools (scenarios with 8-12 cases, true/false with 20-30 questions)
- End with synthesis tools (match concepts with 20-30 pairs, two facts one lie with 15-20 questions)
- Estimate realistic time requirements (15-45 minutes per tool for comprehensive learning)
- Include detailed learning objectives with specific, measurable outcomes
- Make content extremely accurate, comprehensive, and detailed for ${topic}
- Ensure each piece of content is substantial and educational, not superficial
- Include real-world examples, case studies, and practical applications throughout
- Add multiple difficulty levels and progressive complexity
- Provide extensive explanations and context for every concept

CONTENT QUALITY STANDARDS:
- Every explanation must be at least 200-500 words for comprehensive understanding
- Include multiple specific examples and real-world applications for each concept
- Provide extensive context and background information
- Explain the "why" behind every concept with detailed reasoning
- Connect concepts to broader themes and applications across multiple domains
- Include common misconceptions and detailed explanations of how to avoid them
- Add practical tips, best practices, and expert insights
- Ensure content is engaging, thought-provoking, and intellectually challenging
- Use analogies, metaphors, and storytelling to make complex concepts accessible
- Include historical context and future implications where relevant
- Provide multiple perspectives on controversial or complex topics
- Add troubleshooting guides and problem-solving frameworks

FINAL VALIDATION:
- Each tool should provide substantial educational value that justifies 15-45 minutes of learning time
- Content should be comprehensive enough that learners feel they've gained deep expertise
- Include progressive difficulty levels within each tool
- Ensure content is accurate, up-to-date, and reflects current best practices
- Make every piece of content educational and avoid filler or superficial information

Return a structured learning plan with fully populated, extremely detailed tool content that provides exceptional educational value and creates a transformative learning experience.`;
}
