import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";

// Learning Plan Schema
const LearningPlanSchema = z.object({
  topic: z.string().min(1),
  intensity: z.enum(["detailed", "general", "simple"]),
  focusAreas: z.array(z.string()).optional(),
  certificateContext: z.object({
    id: z.string(),
    name: z.string(),
    provider: z.string(),
    description: z.string(),
  }).optional(),
});

// Plan Generation Response Schema
const PlanResponseSchema = z.object({
  planId: z.string(),
  title: z.string(),
  description: z.string(),
  estimatedDuration: z.string(),
  difficulty: z.enum(["Beginner", "Intermediate", "Advanced"]),
  tools: z.array(z.object({
    id: z.string(),
    name: z.string(),
    type: z.enum([
      "flashcards",
      "trueOrFalse",
      "twoFactsOneLie",
      "knowledgeGraphs",
      "practicalScenarioGuidance",
      "matchConcepts",
      "conceptsGuidedLearning"
    ]),
    description: z.string(),
    order: z.number(),
    estimatedTime: z.string(),
    content: z.any(), // Tool-specific content
  })),
  learningObjectives: z.array(z.string()),
  prerequisites: z.array(z.string()).optional(),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const parsed = LearningPlanSchema.safeParse(body);

    if (!parsed.success) {
      return Response.json({
        error: "Invalid payload",
        details: parsed.error.issues
      }, { status: 400 });
    }

    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return Response.json({
        error: "Missing GOOGLE_GENERATIVE_AI_API_KEY"
      }, { status: 500 });
    }

    const { topic, intensity, focusAreas, certificateContext } = parsed.data;

    // Generate unique plan ID
    const planId = `plan_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Build context-aware prompt
    const contextPrompt = buildLearningPlanPrompt({
      topic,
      intensity,
      focusAreas,
      certificateContext,
      planId
    });

    const result = await generateObject({
      model: google("gemini-2.5-pro"),
      maxTokens: 8000,
      schema: PlanResponseSchema,
      prompt: contextPrompt,
    });

    return Response.json({
      plan: result.object,
      usage: result.usage
    });

  } catch (e: any) {
    console.error("Learning Hub API Error:", e);
    return Response.json({
      error: e?.message ?? "Server error"
    }, { status: 500 });
  }
}

function buildLearningPlanPrompt(args: {
  topic: string;
  intensity: "detailed" | "general" | "simple";
  focusAreas?: string[];
  certificateContext?: any;
  planId: string;
}) {
  const { topic, intensity, focusAreas, certificateContext, planId } = args;

  const intensityMap = {
    simple: "beginner-friendly with basic concepts",
    general: "comprehensive coverage with moderate depth",
    detailed: "in-depth analysis with advanced concepts"
  };

  const focusAreasText = focusAreas?.length
    ? `Focus specifically on: ${focusAreas.join(", ")}.`
    : "";

  const certificateText = certificateContext
    ? `This learning plan is for the ${certificateContext.name} certification by ${certificateContext.provider}. Context: ${certificateContext.description}`
    : "";

  return `You are an expert learning designer creating personalized, interactive learning plans.

Create a comprehensive learning plan for the topic: "${topic}"
Intensity level: ${intensityMap[intensity]}
${focusAreasText}
${certificateText}

Design a plan that uses these 7 interactive learning tools strategically:

1. **Flashcards** - For memorization and quick recall
2. **True or False** - For concept validation and misconception correction
3. **Two Facts One Lie** - For critical thinking and detail attention
4. **Knowledge Graphs** - For understanding relationships between concepts
5. **Practical Scenario Guidance** - For real-world application and decision making
6. **Match Concepts** - For connecting related ideas and terminology
7. **Concepts Guided Learning** - For step-by-step concept exploration

Requirements:
- Plan ID: ${planId}
- Create 4-8 learning tools in logical sequence
- Each tool should have specific, actionable content
- Estimate realistic time requirements
- Include clear learning objectives
- Make it engaging and interactive
- Ensure content is accurate and comprehensive

For each tool, provide:
- Specific content (questions, scenarios, concepts, etc.)
- Clear instructions for the learner
- Expected learning outcomes

Return a structured learning plan that will create an exceptional learning experience.`;
}
