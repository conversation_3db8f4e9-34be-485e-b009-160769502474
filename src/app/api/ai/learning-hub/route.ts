import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";

// Learning Plan Schema
const LearningPlanSchema = z.object({
  topic: z.string().min(1),
  intensity: z.enum(["detailed", "general", "simple"]),
  focusAreas: z.array(z.string()).optional(),
  certificateContext: z.object({
    id: z.string(),
    name: z.string(),
    provider: z.string(),
    description: z.string(),
  }).optional(),
});

// Tool Content Schemas
const FlashcardContentSchema = z.object({
  flashcards: z.array(z.object({
    id: z.string(),
    front: z.string(),
    back: z.string(),
    difficulty: z.enum(["easy", "medium", "hard"]).optional(),
  }))
});

const TrueFalseContentSchema = z.object({
  questions: z.array(z.object({
    id: z.string(),
    statement: z.string(),
    correct: z.boolean(),
    explanation: z.string(),
    category: z.string().optional(),
  }))
});

const TwoFactsOneLieContentSchema = z.object({
  questions: z.array(z.object({
    id: z.string(),
    topic: z.string(),
    statements: z.tuple([z.string(), z.string(), z.string()]),
    lieIndex: z.union([z.literal(0), z.literal(1), z.literal(2)]),
    explanations: z.tuple([z.string(), z.string(), z.string()]),
  }))
});

const ConceptsGuidedLearningContentSchema = z.object({
  learningPath: z.object({
    id: z.string(),
    title: z.string(),
    description: z.string(),
    difficulty: z.enum(["beginner", "intermediate", "advanced"]),
    estimatedTime: z.string(),
    steps: z.array(z.object({
      id: z.string(),
      title: z.string(),
      content: z.string(),
      type: z.enum(["introduction", "explanation", "example", "practice", "summary"]),
      keyPoints: z.array(z.string()).optional(),
      examples: z.array(z.string()).optional(),
      practiceQuestion: z.object({
        question: z.string(),
        options: z.array(z.string()),
        correctIndex: z.number(),
        explanation: z.string(),
      }).optional(),
      tips: z.array(z.string()).optional(),
    })),
    learningObjectives: z.array(z.string()),
  })
});

// Plan Generation Response Schema
const PlanResponseSchema = z.object({
  planId: z.string(),
  title: z.string(),
  description: z.string(),
  estimatedDuration: z.string(),
  difficulty: z.enum(["Beginner", "Intermediate", "Advanced"]),
  tools: z.array(z.object({
    id: z.string(),
    name: z.string(),
    type: z.enum([
      "flashcards",
      "trueOrFalse",
      "twoFactsOneLie",
      "knowledgeGraphs",
      "practicalScenarioGuidance",
      "matchConcepts",
      "conceptsGuidedLearning"
    ]),
    description: z.string(),
    order: z.number(),
    estimatedTime: z.string(),
    content: z.union([
      FlashcardContentSchema,
      TrueFalseContentSchema,
      TwoFactsOneLieContentSchema,
      ConceptsGuidedLearningContentSchema,
      z.record(z.string(), z.any()), // For other tool types
    ]),
  })),
  learningObjectives: z.array(z.string()),
  prerequisites: z.array(z.string()).optional(),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const parsed = LearningPlanSchema.safeParse(body);

    if (!parsed.success) {
      return Response.json({
        error: "Invalid payload",
        details: parsed.error.issues
      }, { status: 400 });
    }

    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return Response.json({
        error: "Missing GOOGLE_GENERATIVE_AI_API_KEY"
      }, { status: 500 });
    }

    const { topic, intensity, focusAreas, certificateContext } = parsed.data;

    // Generate unique plan ID
    const planId = `plan_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Build context-aware prompt
    const contextPrompt = buildLearningPlanPrompt({
      topic,
      intensity,
      focusAreas,
      certificateContext,
      planId
    });

    const result = await generateObject({
      model: google("gemini-2.5-pro"),
      maxTokens: 8000,
      schema: PlanResponseSchema,
      prompt: contextPrompt,
    });

    return Response.json({
      plan: result.object,
      usage: result.usage
    });

  } catch (e: any) {
    console.error("Learning Hub API Error:", e);
    return Response.json({
      error: e?.message ?? "Server error"
    }, { status: 500 });
  }
}

function buildLearningPlanPrompt(args: {
  topic: string;
  intensity: "detailed" | "general" | "simple";
  focusAreas?: string[];
  certificateContext?: any;
  planId: string;
}) {
  const { topic, intensity, focusAreas, certificateContext, planId } = args;

  const intensityMap = {
    simple: "beginner-friendly with basic concepts",
    general: "comprehensive coverage with moderate depth",
    detailed: "in-depth analysis with advanced concepts"
  };

  const focusAreasText = focusAreas?.length
    ? `Focus specifically on: ${focusAreas.join(", ")}.`
    : "";

  const certificateText = certificateContext
    ? `This learning plan is for the ${certificateContext.name} certification by ${certificateContext.provider}. Context: ${certificateContext.description}`
    : "";

  return `You are an expert learning designer creating personalized, interactive learning plans.

Create a comprehensive learning plan for the topic: "${topic}"
Intensity level: ${intensityMap[intensity]}
${focusAreasText}
${certificateText}

IMPORTANT: You must provide complete, detailed content for each tool. Here are the exact content structures required:

**For Flashcards tools:**
content: {
  flashcards: [
    { id: "1", front: "Question", back: "Answer", difficulty: "easy|medium|hard" }
  ]
}

**For True/False tools:**
content: {
  questions: [
    {
      id: "1",
      statement: "Statement to evaluate",
      correct: true|false,
      explanation: "Why this is true/false",
      category: "Topic category"
    }
  ]
}

**For Two Facts One Lie tools:**
content: {
  questions: [
    {
      id: "1",
      topic: "Topic name",
      statements: ["Fact 1", "Fact 2", "Lie"],
      lieIndex: 2,
      explanations: ["Why fact 1 is true", "Why fact 2 is true", "Why this is false"]
    }
  ]
}

**For Concepts Guided Learning tools:**
content: {
  learningPath: {
    id: "path1",
    title: "Learning path title",
    description: "Path description",
    difficulty: "beginner|intermediate|advanced",
    estimatedTime: "X minutes",
    steps: [
      {
        id: "step1",
        title: "Step title",
        content: "Detailed explanation with comprehensive information",
        type: "introduction|explanation|example|practice|summary",
        keyPoints: ["Point 1", "Point 2"],
        examples: ["Example 1"],
        practiceQuestion: {
          question: "Practice question",
          options: ["A", "B", "C", "D"],
          correctIndex: 0,
          explanation: "Why this is correct"
        }
      }
    ],
    learningObjectives: ["Objective 1", "Objective 2"]
  }
}

**For Match Concepts tools:**
content: {
  pairs: [
    { id: "1", concept: "Term", definition: "Definition", category: "Category" }
  ]
}

**For Knowledge Graphs tools:**
content: {
  nodes: [
    { id: "1", label: "Concept", type: "concept|principle|application", description: "Description" }
  ],
  edges: [
    { id: "1", source: "1", target: "2", relationship: "relates to|depends on|implements" }
  ]
}

**For Practical Scenario Guidance tools:**
content: {
  scenarios: [
    {
      id: "1",
      title: "Scenario title",
      description: "Scenario description",
      context: "Background context",
      challenge: "What needs to be solved",
      guidanceSteps: [
        { step: 1, action: "Action to take", reasoning: "Why this action" }
      ],
      outcome: "Expected result"
    }
  ]
}

Requirements:
- Plan ID: ${planId}
- Create 4-7 learning tools in logical sequence
- Each tool MUST have complete content as shown above
- Start with conceptual tools (guided learning, flashcards)
- Progress to application tools (scenarios, true/false)
- End with synthesis tools (match concepts, two facts one lie)
- Estimate realistic time requirements (5-20 minutes per tool)
- Include clear learning objectives
- Make content accurate and comprehensive for ${topic}

Return a structured learning plan with fully populated tool content.`;
}
