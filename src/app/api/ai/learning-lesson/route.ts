import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject } from "ai";

// Enhanced Content Schema for different tool types
const EnhancedContentSchema = {
  flashcards: {
    type: "object",
    properties: {
      flashcards: {
        type: "array",
        items: {
          type: "object",
          properties: {
            id: { type: "string" },
            front: { type: "string" },
            back: { type: "string" },
            difficulty: { type: "string", enum: ["easy", "medium", "hard"] },
            category: { type: "string" }
          },
          required: ["id", "front", "back"]
        }
      }
    },
    required: ["flashcards"]
  },
  
  trueOrFalse: {
    type: "object",
    properties: {
      questions: {
        type: "array",
        items: {
          type: "object",
          properties: {
            id: { type: "string" },
            statement: { type: "string" },
            correct: { type: "boolean" },
            explanation: { type: "string" },
            difficulty: { type: "string", enum: ["easy", "medium", "hard"] },
            category: { type: "string" }
          },
          required: ["id", "statement", "correct", "explanation"]
        }
      }
    },
    required: ["questions"]
  },
  
  twoFactsOneLie: {
    type: "object",
    properties: {
      questions: {
        type: "array",
        items: {
          type: "object",
          properties: {
            id: { type: "string" },
            statements: {
              type: "array",
              items: { type: "string" },
              minItems: 3,
              maxItems: 3
            },
            lieIndex: { type: "number", minimum: 0, maximum: 2 },
            explanations: {
              type: "array",
              items: { type: "string" },
              minItems: 3,
              maxItems: 3
            },
            difficulty: { type: "string", enum: ["easy", "medium", "hard"] },
            category: { type: "string" }
          },
          required: ["id", "statements", "lieIndex", "explanations"]
        }
      }
    },
    required: ["questions"]
  },
  
  knowledgeGraphs: {
    type: "object",
    properties: {
      nodes: {
        type: "array",
        items: {
          type: "object",
          properties: {
            id: { type: "string" },
            label: { type: "string" },
            description: { type: "string" },
            category: { type: "string" },
            x: { type: "number" },
            y: { type: "number" },
            color: { type: "string" }
          },
          required: ["id", "label", "description", "x", "y"]
        }
      },
      edges: {
        type: "array",
        items: {
          type: "object",
          properties: {
            id: { type: "string" },
            source: { type: "string" },
            target: { type: "string" },
            label: { type: "string" },
            description: { type: "string" },
            type: { type: "string", enum: ["solid", "dashed", "dotted"] }
          },
          required: ["id", "source", "target", "label", "description"]
        }
      }
    },
    required: ["nodes", "edges"]
  },
  
  practicalScenarioGuidance: {
    type: "object",
    properties: {
      scenarios: {
        type: "array",
        items: {
          type: "object",
          properties: {
            id: { type: "string" },
            title: { type: "string" },
            description: { type: "string" },
            context: { type: "string" },
            difficulty: { type: "string", enum: ["easy", "medium", "hard"] },
            category: { type: "string" },
            steps: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  title: { type: "string" },
                  description: { type: "string" },
                  guidance: { type: "string" },
                  options: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        id: { type: "string" },
                        text: { type: "string" },
                        isCorrect: { type: "boolean" },
                        feedback: { type: "string" },
                        consequence: { type: "string" }
                      },
                      required: ["id", "text", "isCorrect", "feedback"]
                    }
                  }
                },
                required: ["id", "title", "description", "options"]
              }
            }
          },
          required: ["id", "title", "description", "context", "steps"]
        }
      }
    },
    required: ["scenarios"]
  },
  
  matchConcepts: {
    type: "object",
    properties: {
      pairs: {
        type: "array",
        items: {
          type: "object",
          properties: {
            id: { type: "string" },
            concept: { type: "string" },
            definition: { type: "string" },
            category: { type: "string" },
            difficulty: { type: "string", enum: ["easy", "medium", "hard"] }
          },
          required: ["id", "concept", "definition"]
        }
      }
    },
    required: ["pairs"]
  },
  
  conceptsGuidedLearning: {
    type: "object",
    properties: {
      steps: {
        type: "array",
        items: {
          type: "object",
          properties: {
            id: { type: "string" },
            title: { type: "string" },
            content: { type: "string" },
            keyPoints: {
              type: "array",
              items: { type: "string" }
            },
            examples: {
              type: "array",
              items: { type: "string" }
            },
            tips: {
              type: "array",
              items: { type: "string" }
            },
            difficulty: { type: "string", enum: ["easy", "medium", "hard"] },
            estimatedTime: { type: "string" }
          },
          required: ["id", "title", "content", "keyPoints"]
        }
      },
      learningObjectives: {
        type: "array",
        items: { type: "string" }
      }
    },
    required: ["steps"]
  }
};

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { toolType, toolContent, topic, userProgress, difficulty } = body;

    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return Response.json({
        error: "Missing GOOGLE_GENERATIVE_AI_API_KEY"
      }, { status: 500 });
    }

    // Get the appropriate schema for the tool type
    const schema = EnhancedContentSchema[toolType as keyof typeof EnhancedContentSchema];
    if (!schema) {
      return Response.json({
        error: "Unsupported tool type"
      }, { status: 400 });
    }

    // Build enhancement prompt based on tool type and user progress
    const enhancementPrompt = buildEnhancementPrompt({
      toolType,
      toolContent,
      topic,
      userProgress,
      difficulty
    });

    const result = await generateObject({
      model: google("gemini-2.5-pro"),
      maxTokens: 16000,
      schema,
      prompt: enhancementPrompt,
    });

    return Response.json({
      enhancedContent: result.object,
      usage: result.usage
    });

  } catch (e: any) {
    console.error("Learning Lesson Enhancement API Error:", e);
    return Response.json({
      error: e?.message ?? "Server error"
    }, { status: 500 });
  }
}

function buildEnhancementPrompt(args: {
  toolType: string;
  toolContent: any;
  topic: string;
  userProgress: number;
  difficulty: string;
}) {
  const { toolType, toolContent, topic, userProgress, difficulty } = args;

  const basePrompt = `
ENHANCE AND IMPROVE the existing ${toolType} content for the topic: "${topic}"

Current user progress: ${userProgress}%
Difficulty level: ${difficulty}

ENHANCEMENT REQUIREMENTS:
- Make content more engaging and interactive
- Add progressive difficulty based on user progress
- Include real-world examples and practical applications
- Ensure educational value and accuracy
- Maintain consistency with the topic and difficulty level
- Add variety and depth to existing content

Current content to enhance:
${JSON.stringify(toolContent, null, 2)}
`;

  const toolSpecificInstructions = {
    flashcards: `
FLASHCARDS ENHANCEMENT:
- Add 5-10 more high-quality flashcards
- Include difficulty progression (easy → medium → hard)
- Add category tags for better organization
- Ensure front/back content is clear and educational
- Include memory techniques and mnemonics where appropriate
- Add visual descriptions or analogies
`,

    trueOrFalse: `
TRUE/FALSE ENHANCEMENT:
- Add 10-15 more challenging questions
- Include detailed explanations for both correct and incorrect answers
- Add real-world context and scenarios
- Include common misconceptions as false statements
- Vary difficulty levels appropriately
- Add category tags for organization
`,

    twoFactsOneLie: `
TWO FACTS ONE LIE ENHANCEMENT:
- Add 8-12 more sophisticated questions
- Make lies plausible but clearly incorrect
- Include comprehensive explanations for all statements
- Add real-world context and examples
- Ensure facts are current and accurate
- Include difficulty progression
`,

    knowledgeGraphs: `
KNOWLEDGE GRAPH ENHANCEMENT:
- Add 10-15 more nodes with rich descriptions
- Create 15-25 meaningful connections (edges)
- Position nodes logically (x, y coordinates from -300 to 300)
- Use appropriate colors for different categories
- Include detailed relationship descriptions
- Ensure comprehensive coverage of the topic
`,

    practicalScenarioGuidance: `
PRACTICAL SCENARIOS ENHANCEMENT:
- Add 3-5 more realistic scenarios
- Include multi-step decision trees
- Add consequences for each choice
- Include expert guidance and tips
- Make scenarios progressively challenging
- Add real-world context and industry examples
`,

    matchConcepts: `
MATCH CONCEPTS ENHANCEMENT:
- Add 15-20 more concept-definition pairs
- Include various difficulty levels
- Add category tags for organization
- Ensure definitions are clear and comprehensive
- Include related concepts and cross-references
- Add context and examples where helpful
`,

    conceptsGuidedLearning: `
GUIDED LEARNING ENHANCEMENT:
- Add 5-10 more detailed learning steps
- Include comprehensive explanations (300-500 words each)
- Add practical examples and case studies
- Include key points, tips, and best practices
- Ensure logical progression and flow
- Add estimated reading times
- Include learning objectives
`
  };

  return basePrompt + (toolSpecificInstructions[toolType as keyof typeof toolSpecificInstructions] || '') + `

CRITICAL REQUIREMENTS:
- All content must be accurate and educational
- Maintain consistency with the topic "${topic}"
- Ensure appropriate difficulty for "${difficulty}" level
- Add substantial value over existing content
- Include progressive learning elements
- Make content engaging and interactive
- Ensure all required fields are properly filled
- Use clear, professional language
- Include practical applications and examples

Return enhanced content that provides exceptional educational value and creates an improved learning experience.`;
}
